# 🚨 URGENT: Netlify Environment Variables Fix

## The Problem
The ICU Dataset Application is still showing backend connection errors because <PERSON><PERSON> isn't loading the environment variables properly during build time.

## 🔧 IMMEDIATE FIX REQUIRED

### Step 1: Add Environment Variables in Netlify Dashboard

**Go to Netlify Dashboard NOW:**
1. Visit: https://app.netlify.com/sites/bespoke-panda-4b21b2/settings/env
2. Click "Add new variable" for each of these:

```
REACT_APP_BACKEND_URL = http://*************:5000
REACT_APP_AWS_IDENTITY_POOL_ID = ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION = ap-southeast-2
REACT_APP_S3_BUCKET = icudatasetphrasesfortesting
REACT_APP_NAME = ICU Dataset Application
REACT_APP_VERSION = 1.0.0
NODE_ENV = production
REACT_APP_DEBUG = false
```

### Step 2: Trigger New Deploy
After adding variables:
1. Go to: https://app.netlify.com/sites/bespoke-panda-4b21b2/deploys
2. Click "Trigger deploy" → "Deploy site"
3. Wait for build to complete

### Step 3: Test the Fix
1. Visit: https://icuphrasecollection.com/debug-netlify-env.html
2. Check if environment variables are now showing
3. Test backend connection

## 🔍 Debug Steps

### Check Current Status:
Visit this debug page after deployment:
**https://icuphrasecollection.com/debug-netlify-env.html**

This will show:
- ✅ Which environment variables are loaded
- ❌ Which ones are missing
- 🌐 Backend connectivity test results

### Expected Results After Fix:
- ✅ REACT_APP_BACKEND_URL should show: http://*************:5000
- ✅ Backend connection test should succeed
- ✅ Video uploads should work on the main app

## 🚨 Why This Happened

**Root Cause:** Netlify doesn't always read environment variables from netlify.toml during build time. The most reliable method is setting them directly in the Netlify UI.

**Evidence:** The error message "Cannot connect to backend server" indicates the frontend is still trying to connect to localhost:5000 instead of the EC2 server.

## ⚡ Alternative Quick Fix

If the above doesn't work immediately, we can also:

1. **Hardcode the production URL temporarily:**
   - Edit the code to use the EC2 URL directly in production
   - This is not ideal but will get the app working immediately

2. **Use a different environment variable approach:**
   - Create a config.js file with the production URLs
   - Import this in the components that need backend access

## 🎯 Success Criteria

After implementing the fix:
- ✅ https://icuphrasecollection.com should connect to EC2 backend
- ✅ Video uploads should work without "Cannot connect to backend server" errors
- ✅ Enhanced 1.5x video capture should be functional
- ✅ Medical dataset collection should be operational

## 🏥 CRITICAL IMPORTANCE

This fix is blocking medical research for ICU patient communication. Every minute the app is down means potential loss of valuable medical data collection opportunities.

**IMPLEMENT THIS FIX IMMEDIATELY!**
