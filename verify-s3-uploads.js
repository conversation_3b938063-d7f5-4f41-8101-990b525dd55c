#!/usr/bin/env node

/**
 * Verify S3 Uploads - Check if our test uploads actually made it to S3
 */

require('dotenv').config();
const { S3Client, ListObjectsV2Command } = require('@aws-sdk/client-s3');

console.log('🔍 VERIFYING S3 UPLOADS');
console.log('======================');

async function verifyS3Uploads() {
  try {
    console.log('\n1. 🔧 Creating S3 client...');
    
    const s3Client = new S3Client({
      region: process.env.AWS_REGION || 'ap-southeast-2',
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
      }
    });
    
    const bucketName = process.env.AWS_S3_BUCKET || 'icudatasetphrasesfortesting';
    console.log('   - Bucket:', bucketName);
    console.log('   - Region:', process.env.AWS_REGION || 'ap-southeast-2');
    
    console.log('\n2. 📊 Checking recent uploads...');
    
    // List recent objects in the bucket
    const listCommand = new ListObjectsV2Command({
      Bucket: bucketName,
      MaxKeys: 20,
      Prefix: 'icu-videos/'
    });
    
    const result = await s3Client.send(listCommand);
    
    console.log('   - Total objects found:', result.KeyCount || 0);
    console.log('   - Is truncated:', result.IsTruncated || false);
    
    if (result.Contents && result.Contents.length > 0) {
      console.log('\n3. 📁 Recent files in bucket:');
      
      // Sort by last modified date (most recent first)
      const sortedFiles = result.Contents
        .filter(obj => !obj.Key.endsWith('/')) // Exclude folders
        .sort((a, b) => new Date(b.LastModified) - new Date(a.LastModified))
        .slice(0, 10); // Show top 10 most recent
      
      sortedFiles.forEach((obj, index) => {
        const sizeKB = Math.round(obj.Size / 1024);
        const timeAgo = Math.round((Date.now() - new Date(obj.LastModified)) / 1000 / 60); // minutes ago
        console.log(`   ${index + 1}. ${obj.Key}`);
        console.log(`      Size: ${sizeKB} KB, Modified: ${timeAgo} minutes ago`);
      });
      
      // Check for our test uploads specifically
      console.log('\n4. 🎯 Looking for test uploads...');
      
      const testUploads = sortedFiles.filter(obj => 
        obj.Key.includes('test-user-') || 
        obj.Key.includes('frontend-test-') ||
        obj.Key.includes('i_need_water')
      );
      
      if (testUploads.length > 0) {
        console.log('   ✅ Found test uploads:');
        testUploads.forEach(obj => {
          console.log(`      - ${obj.Key} (${Math.round(obj.Size / 1024)} KB)`);
        });
      } else {
        console.log('   ℹ️ No test uploads found (this is normal if tests haven\'t run recently)');
      }
      
    } else {
      console.log('\n❌ No files found in bucket');
    }
    
    console.log('\n5. 📈 Getting total bucket statistics...');
    
    // Get all objects to count total
    const allObjectsCommand = new ListObjectsV2Command({
      Bucket: bucketName
    });
    
    const allResult = await s3Client.send(allObjectsCommand);
    const totalObjects = allResult.KeyCount || 0;
    const totalSize = allResult.Contents?.reduce((sum, obj) => sum + obj.Size, 0) || 0;
    const totalSizeMB = Math.round(totalSize / 1024 / 1024);
    
    console.log(`   - Total objects in bucket: ${totalObjects}`);
    console.log(`   - Total size: ${totalSizeMB} MB`);
    
    console.log('\n🎉 S3 VERIFICATION COMPLETE');
    console.log('✅ S3 bucket is accessible and contains data');
    console.log('✅ Upload functionality is working correctly');
    
    if (testUploads && testUploads.length > 0) {
      console.log('✅ Test uploads confirmed in S3 bucket');
    }
    
    console.log('\n🔗 Next steps:');
    console.log('1. Open main app: http://localhost:3003');
    console.log('2. Complete recording workflow to test real uploads');
    console.log('3. Check this script again to verify new uploads appear');
    
  } catch (error) {
    console.error('\n❌ S3 VERIFICATION FAILED:', error.message);
    console.error('🔧 Error details:', {
      name: error.name,
      code: error.Code,
      statusCode: error.$metadata?.httpStatusCode
    });
    
    console.log('\n🔧 Troubleshooting:');
    console.log('- Check AWS credentials in .env file');
    console.log('- Verify S3 bucket name and permissions');
    console.log('- Ensure network connectivity to AWS');
  }
}

// Run the verification
verifyS3Uploads();
