#!/usr/bin/env node

/**
 * Comprehensive Upload Fix Test
 * Tests all upload functionality after bug fixes
 */

require('dotenv').config();
const { S3Client, ListObjectsV2Command, PutObjectCommand } = require('@aws-sdk/client-s3');

console.log('🔍 COMPREHENSIVE UPLOAD FIX TEST');
console.log('================================');

// Test 1: Environment Variables
console.log('\n1. 📋 Environment Variables Check:');
console.log('   - AWS_ACCESS_KEY_ID:', process.env.AWS_ACCESS_KEY_ID ? 'SET (' + process.env.AWS_ACCESS_KEY_ID.substring(0,8) + '...)' : 'NOT SET');
console.log('   - AWS_SECRET_ACCESS_KEY:', process.env.AWS_SECRET_ACCESS_KEY ? 'SET (****)' : 'NOT SET');
console.log('   - AWS_REGION:', process.env.AWS_REGION || 'NOT SET');
console.log('   - AWS_S3_BUCKET:', process.env.AWS_S3_BUCKET || 'NOT SET');
console.log('   - REACT_APP_AWS_IDENTITY_POOL_ID:', process.env.REACT_APP_AWS_IDENTITY_POOL_ID ? 'SET (' + process.env.REACT_APP_AWS_IDENTITY_POOL_ID.substring(0,20) + '...)' : 'NOT SET');

// Test 2: S3 Client Creation (Backend Style)
console.log('\n2. 🔧 S3 Client Creation Test:');
try {
  const s3Client = new S3Client({
    region: process.env.AWS_REGION || 'ap-southeast-2',
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    }
  });
  console.log('   ✅ S3 Client created successfully');
  
  // Test 3: S3 Connectivity
  console.log('\n3. 🌐 S3 Connectivity Test:');
  const bucketName = process.env.AWS_S3_BUCKET || 'icudatasetphrasesfortesting';
  
  const listCommand = new ListObjectsV2Command({
    Bucket: bucketName,
    MaxKeys: 5
  });
  
  s3Client.send(listCommand).then(result => {
    console.log('   ✅ S3 connectivity successful');
    console.log('   📊 Bucket contents:', {
      bucket: bucketName,
      objectCount: result.KeyCount || 0,
      objects: result.Contents?.slice(0, 3).map(obj => obj.Key) || []
    });
    
    // Test 4: Test Upload
    console.log('\n4. 📤 Test Upload:');
    const testKey = `test-uploads/upload-fix-test-${Date.now()}.txt`;
    const testContent = `Upload fix test - ${new Date().toISOString()}`;
    
    const uploadCommand = new PutObjectCommand({
      Bucket: bucketName,
      Key: testKey,
      Body: testContent,
      ContentType: 'text/plain',
      Metadata: {
        'test-type': 'upload-fix-verification',
        'timestamp': new Date().toISOString()
      }
    });
    
    return s3Client.send(uploadCommand);
  }).then(uploadResult => {
    console.log('   ✅ Test upload successful!');
    console.log('   📊 Upload result:', {
      ETag: uploadResult.ETag,
      Location: uploadResult.Location
    });
    
    console.log('\n🎉 ALL TESTS PASSED - UPLOAD FUNCTIONALITY FIXED!');
    console.log('✅ Environment variables configured');
    console.log('✅ S3 client creation working');
    console.log('✅ S3 connectivity established');
    console.log('✅ Upload functionality verified');
    
  }).catch(error => {
    console.error('   ❌ S3 operation failed:', error.message);
    console.error('   🔧 Error details:', {
      name: error.name,
      code: error.Code,
      statusCode: error.$metadata?.httpStatusCode
    });
  });
  
} catch (error) {
  console.error('   ❌ S3 Client creation failed:', error.message);
  console.error('   🔧 Check your AWS credentials and configuration');
}

// Test 5: Backend Server Test
console.log('\n5. 🖥️ Backend Server Test:');
const http = require('http');

const testBackend = () => {
  const options = {
    hostname: 'localhost',
    port: 5000,
    path: '/health',
    method: 'GET',
    timeout: 5000
  };

  const req = http.request(options, (res) => {
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    res.on('end', () => {
      if (res.statusCode === 200) {
        console.log('   ✅ Backend server responding');
        try {
          const healthData = JSON.parse(data);
          console.log('   📊 Server status:', {
            status: healthData.status,
            aws: healthData.services?.aws,
            uptime: Math.round(healthData.uptime) + 's'
          });
        } catch (e) {
          console.log('   📊 Server response:', data.substring(0, 100));
        }
      } else {
        console.log('   ⚠️ Backend server returned status:', res.statusCode);
      }
    });
  });

  req.on('error', (error) => {
    console.log('   ❌ Backend server not responding:', error.message);
    console.log('   💡 Make sure to start the backend server: node server.js');
  });

  req.on('timeout', () => {
    console.log('   ⏰ Backend server request timed out');
    req.destroy();
  });

  req.end();
};

// Wait a moment then test backend
setTimeout(testBackend, 1000);

// Test 6: React App Test
console.log('\n6. ⚛️ React App Test:');
const testReactApp = () => {
  const options = {
    hostname: 'localhost',
    port: 3003,
    path: '/',
    method: 'GET',
    timeout: 5000
  };

  const req = http.request(options, (res) => {
    if (res.statusCode === 200) {
      console.log('   ✅ React app responding on port 3003');
    } else {
      console.log('   ⚠️ React app returned status:', res.statusCode);
    }
  });

  req.on('error', (error) => {
    console.log('   ❌ React app not responding:', error.message);
    console.log('   💡 Make sure to start the React app: PORT=3003 npm start');
  });

  req.on('timeout', () => {
    console.log('   ⏰ React app request timed out');
    req.destroy();
  });

  req.end();
};

// Wait a moment then test React app
setTimeout(testReactApp, 2000);

console.log('\n🔗 Test URLs:');
console.log('   - Backend Health: http://localhost:5000/health');
console.log('   - Backend S3 Test: http://localhost:5000/api/test-s3');
console.log('   - React App: http://localhost:3003');
console.log('   - Upload Test: http://localhost:3003/upload-mode-diagnostic.html');
