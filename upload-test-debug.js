// Upload Process Debug Test for ICU Dataset Application
const { uploadVideoToS3 } = require('./src/services/awsStorage');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

console.log('🔍 UPLOAD PROCESS DEBUG TEST');
console.log('============================');

// Test configuration
const testConfig = {
  phrase: 'test-phrase',
  demographics: {
    userId: 'test-user',
    ageGroup: '40to64',
    gender: 'female',
    ethnicity: 'caucasian',
    category: 'test'
  },
  recordingNumber: 1
};

// Create a mock video blob for testing
function createMockVideoBlob() {
  // Create a small test video file (just some bytes to simulate)
  const mockVideoData = Buffer.from('mock-video-data-for-testing');
  
  // Convert to Blob-like object for testing
  return {
    size: mockVideoData.length,
    type: 'video/webm',
    arrayBuffer: () => Promise.resolve(mockVideoData.buffer),
    stream: () => new ReadableStream({
      start(controller) {
        controller.enqueue(mockVideoData);
        controller.close();
      }
    })
  };
}

async function testUploadProcess() {
  console.log('\n🎯 STEP 1: Environment Check');
  console.log('----------------------------');
  
  const envVars = {
    'REACT_APP_AWS_IDENTITY_POOL_ID': process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
    'REACT_APP_AWS_REGION': process.env.REACT_APP_AWS_REGION,
    'REACT_APP_S3_BUCKET': process.env.REACT_APP_S3_BUCKET,
    'REACT_APP_BACKEND_URL': process.env.REACT_APP_BACKEND_URL
  };

  Object.entries(envVars).forEach(([key, value]) => {
    if (value) {
      console.log(`✅ ${key}: ${key.includes('IDENTITY_POOL') ? value.substring(0, 20) + '...' : value}`);
    } else {
      console.log(`❌ ${key}: NOT SET`);
    }
  });

  console.log('\n🎯 STEP 2: Upload Mode Detection');
  console.log('--------------------------------');
  
  const hasIdentityPool = !!process.env.REACT_APP_AWS_IDENTITY_POOL_ID;
  const hasRegion = !!process.env.REACT_APP_AWS_REGION;
  const hasBucket = !!process.env.REACT_APP_S3_BUCKET;
  const forceBackendMode = false; // From awsStorage.js
  
  const shouldUseDirect = hasIdentityPool && hasRegion && hasBucket && !forceBackendMode;
  
  console.log(`📊 AWS Configuration Check:`);
  console.log(`   - Identity Pool: ${hasIdentityPool ? '✅' : '❌'}`);
  console.log(`   - Region: ${hasRegion ? '✅' : '❌'}`);
  console.log(`   - Bucket: ${hasBucket ? '✅' : '❌'}`);
  console.log(`   - Force Backend Mode: ${forceBackendMode ? '✅' : '❌'}`);
  console.log(`   - Expected Upload Mode: ${shouldUseDirect ? 'DIRECT S3' : 'BACKEND'}`);

  console.log('\n🎯 STEP 3: Mock Upload Test');
  console.log('---------------------------');
  
  try {
    const mockBlob = createMockVideoBlob();
    console.log(`📊 Mock video blob created:`);
    console.log(`   - Size: ${mockBlob.size} bytes`);
    console.log(`   - Type: ${mockBlob.type}`);
    
    console.log('\n🚀 Attempting upload...');
    
    // This will fail in Node.js environment, but we can catch the specific error
    const result = await uploadVideoToS3(
      mockBlob,
      testConfig.phrase,
      testConfig.demographics,
      testConfig.recordingNumber
    );
    
    console.log('✅ Upload successful:', result);
    
  } catch (error) {
    console.log('❌ Upload failed with error:');
    console.log(`   - Name: ${error.name}`);
    console.log(`   - Message: ${error.message}`);
    console.log(`   - Code: ${error.code || 'N/A'}`);
    
    // Analyze the error type
    if (error.message.includes('fetch')) {
      console.log('🔍 Analysis: Network/fetch error - likely backend connectivity issue');
    } else if (error.message.includes('CORS')) {
      console.log('🔍 Analysis: CORS error - S3 bucket policy issue');
    } else if (error.message.includes('credentials')) {
      console.log('🔍 Analysis: AWS credentials error');
    } else if (error.message.includes('AccessDenied')) {
      console.log('🔍 Analysis: AWS permissions error');
    } else if (error.message.includes('window') || error.message.includes('document')) {
      console.log('🔍 Analysis: Browser-specific code running in Node.js (expected)');
    } else {
      console.log('🔍 Analysis: Unknown error type');
    }
  }

  console.log('\n🎯 STEP 4: Backend Connectivity Test');
  console.log('------------------------------------');
  
  try {
    const backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';
    console.log(`🔍 Testing backend at: ${backendUrl}`);
    
    // Test health endpoint
    const response = await fetch(`${backendUrl}/health`);
    if (response.ok) {
      const healthData = await response.json();
      console.log('✅ Backend health check passed');
      console.log(`   - Status: ${healthData.status}`);
      console.log(`   - Uptime: ${healthData.uptime}s`);
      console.log(`   - AWS Status: ${healthData.services?.aws || 'unknown'}`);
    } else {
      console.log(`❌ Backend health check failed: HTTP ${response.status}`);
    }
    
  } catch (error) {
    console.log('❌ Backend connectivity test failed:');
    console.log(`   - Error: ${error.message}`);
  }

  console.log('\n🎯 SUMMARY');
  console.log('----------');
  console.log('This test helps identify upload issues in the ICU Dataset Application.');
  console.log('Check the analysis above to understand where the upload process is failing.');
}

// Run the test
testUploadProcess().catch(error => {
  console.error('💥 Test execution failed:', error.message);
});
