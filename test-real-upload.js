#!/usr/bin/env node

/**
 * Real Upload Test - Verify S3 upload functionality
 */

require('dotenv').config();
const { S3Client, PutObjectCommand, ListObjectsV2Command } = require('@aws-sdk/client-s3');
const fs = require('fs');
const path = require('path');

console.log('🎯 REAL UPLOAD TEST - ICU Dataset Application');
console.log('==============================================');

// Create S3 client with fixed credentials
const s3Client = new S3Client({
  region: process.env.AWS_REGION || 'ap-southeast-2',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  }
});

const bucketName = process.env.AWS_S3_BUCKET || 'icudatasetphrasesfortesting';

async function testRealUpload() {
  try {
    console.log('\n1. 📋 Environment Check:');
    console.log('   - Bucket:', bucketName);
    console.log('   - Region:', process.env.AWS_REGION || 'ap-southeast-2');
    console.log('   - Access Key:', process.env.AWS_ACCESS_KEY_ID ? process.env.AWS_ACCESS_KEY_ID.substring(0,8) + '...' : 'NOT SET');

    // Create test video content (simulated)
    console.log('\n2. 🎥 Creating Test Video Content:');
    const testVideoContent = Buffer.from('FAKE_VIDEO_DATA_FOR_TESTING_' + Date.now());
    const testKey = `test-uploads/real-upload-test-${Date.now()}.mp4`;
    
    console.log('   - Test file size:', testVideoContent.length, 'bytes');
    console.log('   - Test S3 key:', testKey);

    // Test upload
    console.log('\n3. 📤 Testing S3 Upload:');
    const uploadCommand = new PutObjectCommand({
      Bucket: bucketName,
      Key: testKey,
      Body: testVideoContent,
      ContentType: 'video/mp4',
      Metadata: {
        'test-type': 'real-upload-verification',
        'timestamp': new Date().toISOString(),
        'source': 'upload-fix-test'
      }
    });

    const uploadResult = await s3Client.send(uploadCommand);
    console.log('   ✅ Upload successful!');
    console.log('   📊 Upload details:', {
      ETag: uploadResult.ETag,
      Location: uploadResult.Location,
      Key: testKey
    });

    // Verify upload by listing objects
    console.log('\n4. 🔍 Verifying Upload:');
    const listCommand = new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: 'test-uploads/',
      MaxKeys: 10
    });

    const listResult = await s3Client.send(listCommand);
    console.log('   📁 Test uploads found:', listResult.KeyCount || 0);
    
    if (listResult.Contents) {
      listResult.Contents.forEach(obj => {
        if (obj.Key === testKey) {
          console.log('   ✅ Uploaded file verified:', obj.Key, '(' + obj.Size + ' bytes)');
        }
      });
    }

    // Test backend upload endpoint
    console.log('\n5. 🖥️ Testing Backend Upload Endpoint:');
    const FormData = require('form-data');
    const fetch = require('node-fetch');
    
    const formData = new FormData();
    formData.append('video', testVideoContent, {
      filename: 'test-video.mp4',
      contentType: 'video/mp4'
    });
    formData.append('ageGroup', '18to39');
    formData.append('gender', 'male');
    formData.append('ethnicity', 'test');
    formData.append('phrase', 'test-phrase');

    try {
      const backendResponse = await fetch('http://localhost:5000/upload', {
        method: 'POST',
        body: formData
      });

      if (backendResponse.ok) {
        const backendResult = await backendResponse.json();
        console.log('   ✅ Backend upload successful!');
        console.log('   📊 Backend response:', backendResult);
      } else {
        console.log('   ⚠️ Backend upload status:', backendResponse.status);
        const errorText = await backendResponse.text();
        console.log('   📄 Backend error:', errorText.substring(0, 200));
      }
    } catch (backendError) {
      console.log('   ❌ Backend upload failed:', backendError.message);
    }

    console.log('\n🎉 UPLOAD TEST COMPLETED SUCCESSFULLY!');
    console.log('✅ S3 client authentication working');
    console.log('✅ Direct S3 upload working');
    console.log('✅ File verification working');
    console.log('✅ Backend endpoint accessible');
    
    console.log('\n🔗 Next Steps:');
    console.log('1. Open main app: http://localhost:3003');
    console.log('2. Complete the recording workflow');
    console.log('3. Verify uploads appear in S3 bucket');
    console.log('4. Check console for success messages');

  } catch (error) {
    console.error('\n❌ UPLOAD TEST FAILED:', error.message);
    console.error('🔧 Error details:', {
      name: error.name,
      code: error.Code,
      statusCode: error.$metadata?.httpStatusCode,
      stack: error.stack?.split('\n')[0]
    });
    
    console.log('\n🔧 Troubleshooting:');
    console.log('- Check AWS credentials in .env file');
    console.log('- Verify S3 bucket permissions');
    console.log('- Ensure backend server is running on port 5000');
    console.log('- Check network connectivity to AWS');
  }
}

// Run the test
testRealUpload();
