<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Netlify Environment Debug - ICU Dataset App</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .env-var { background: #f8f9fa; padding: 10px; margin: 5px 0; border-left: 4px solid #007bff; }
        .missing { border-left-color: #dc3545; background: #f8d7da; }
        .present { border-left-color: #28a745; background: #d4edda; }
        .test-result { padding: 15px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Netlify Environment Variables Debug</h1>
        <p><strong>Current URL:</strong> <span id="current-url"></span></p>
        <p><strong>Timestamp:</strong> <span id="timestamp"></span></p>
        
        <h2>📋 Environment Variables Check</h2>
        <div id="env-check"></div>
        
        <h2>🌐 Backend Connectivity Test</h2>
        <button onclick="testBackendConnection()">Test Backend Connection</button>
        <div id="backend-test"></div>
        
        <h2>🔧 Build Information</h2>
        <div id="build-info"></div>
        
        <h2>📱 User Agent & Browser Info</h2>
        <div id="browser-info"></div>
    </div>

    <script>
        // Display current URL and timestamp
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('timestamp').textContent = new Date().toISOString();
        
        // Check environment variables
        function checkEnvironmentVariables() {
            const envVars = {
                'REACT_APP_BACKEND_URL': process.env.REACT_APP_BACKEND_URL,
                'REACT_APP_AWS_IDENTITY_POOL_ID': process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
                'REACT_APP_AWS_REGION': process.env.REACT_APP_AWS_REGION,
                'REACT_APP_S3_BUCKET': process.env.REACT_APP_S3_BUCKET,
                'NODE_ENV': process.env.NODE_ENV,
                'REACT_APP_DEBUG': process.env.REACT_APP_DEBUG,
                'REACT_APP_NAME': process.env.REACT_APP_NAME,
                'REACT_APP_VERSION': process.env.REACT_APP_VERSION
            };
            
            const envCheckDiv = document.getElementById('env-check');
            let html = '';
            let missingCount = 0;
            
            for (const [key, value] of Object.entries(envVars)) {
                const isPresent = value !== undefined && value !== null && value !== '';
                const cssClass = isPresent ? 'present' : 'missing';
                
                if (!isPresent) missingCount++;
                
                html += `
                    <div class="env-var ${cssClass}">
                        <strong>${key}:</strong> ${isPresent ? value : '❌ MISSING'}
                    </div>
                `;
            }
            
            // Add summary
            if (missingCount > 0) {
                html = `<div class="test-result error">
                    ❌ <strong>${missingCount} environment variables are missing!</strong><br>
                    This explains why the backend connection is failing.
                </div>` + html;
            } else {
                html = `<div class="test-result success">
                    ✅ <strong>All environment variables are present!</strong>
                </div>` + html;
            }
            
            envCheckDiv.innerHTML = html;
            return envVars;
        }
        
        // Test backend connection
        async function testBackendConnection() {
            const resultDiv = document.getElementById('backend-test');
            resultDiv.innerHTML = '<div class="test-result warning">🔄 Testing backend connection...</div>';
            
            const envVars = checkEnvironmentVariables();
            const backendUrl = envVars.REACT_APP_BACKEND_URL || 'http://localhost:5000';
            
            try {
                console.log('Testing backend URL:', backendUrl);
                
                const response = await fetch(`${backendUrl}/health`, {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ <strong>Backend Connection Successful!</strong><br>
                            <strong>URL:</strong> ${backendUrl}<br>
                            <strong>Status:</strong> ${data.status}<br>
                            <strong>Uptime:</strong> ${data.uptime}s<br>
                            <strong>AWS:</strong> ${data.services?.aws || 'unknown'}
                        </div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="test-result error">
                            ❌ <strong>Backend Connection Failed!</strong><br>
                            <strong>URL:</strong> ${backendUrl}<br>
                            <strong>Status:</strong> ${response.status} ${response.statusText}<br>
                            <strong>Possible Issues:</strong><br>
                            • Backend server not running on EC2<br>
                            • Security group blocking port 5000<br>
                            • CORS not configured for Netlify domain
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ <strong>Backend Connection Error!</strong><br>
                        <strong>URL:</strong> ${backendUrl}<br>
                        <strong>Error:</strong> ${error.message}<br>
                        <strong>Likely Causes:</strong><br>
                        • Environment variable REACT_APP_BACKEND_URL not set correctly<br>
                        • Backend server not accessible from internet<br>
                        • Network/firewall blocking connection<br>
                        • CORS policy blocking cross-origin requests
                    </div>
                `;
                console.error('Backend connection error:', error);
            }
        }
        
        // Display build information
        function displayBuildInfo() {
            const buildInfoDiv = document.getElementById('build-info');
            buildInfoDiv.innerHTML = `
                <div class="env-var">
                    <strong>Build Time:</strong> ${new Date().toISOString()}
                </div>
                <div class="env-var">
                    <strong>User Agent:</strong> ${navigator.userAgent}
                </div>
                <div class="env-var">
                    <strong>Platform:</strong> ${navigator.platform}
                </div>
                <div class="env-var">
                    <strong>Language:</strong> ${navigator.language}
                </div>
                <div class="env-var">
                    <strong>Online:</strong> ${navigator.onLine ? '✅ Yes' : '❌ No'}
                </div>
            `;
        }
        
        // Display browser info
        function displayBrowserInfo() {
            const browserInfoDiv = document.getElementById('browser-info');
            browserInfoDiv.innerHTML = `
                <div class="env-var">
                    <strong>Window Location:</strong> ${window.location.href}
                </div>
                <div class="env-var">
                    <strong>Protocol:</strong> ${window.location.protocol}
                </div>
                <div class="env-var">
                    <strong>Host:</strong> ${window.location.host}
                </div>
                <div class="env-var">
                    <strong>Pathname:</strong> ${window.location.pathname}
                </div>
                <div class="env-var">
                    <strong>Referrer:</strong> ${document.referrer || 'None'}
                </div>
            `;
        }
        
        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkEnvironmentVariables();
            displayBuildInfo();
            displayBrowserInfo();
        });
    </script>
</body>
</html>
