/**
 * Production Configuration for ICU Dataset Application
 * This file provides fallback configuration when environment variables aren't loaded properly
 */

// Detect if we're running in production (Netlify)
const isNetlifyProduction = () => {
  return window.location.hostname === 'icuphrasecollection.com' || 
         window.location.hostname.includes('netlify.app');
};

// Production configuration
const PRODUCTION_CONFIG = {
  BACKEND_URL: 'http://*************:5000',
  AWS_IDENTITY_POOL_ID: 'ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd',
  AWS_REGION: 'ap-southeast-2',
  S3_BUCKET: 'icudatasetphrasesfortesting',
  APP_NAME: 'ICU Dataset Application',
  APP_VERSION: '1.0.0',
  DEBUG: false
};

// Development configuration
const DEVELOPMENT_CONFIG = {
  BACKEND_URL: 'http://localhost:5000',
  AWS_IDENTITY_POOL_ID: 'ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd',
  AWS_REGION: 'ap-southeast-2',
  S3_BUCKET: 'icudatasetphrasesfortesting',
  APP_NAME: 'ICU Dataset Application',
  APP_VERSION: '1.0.0',
  DEBUG: true
};

/**
 * Get configuration value with fallback logic
 * Priority: 1. Environment variable, 2. Production config, 3. Development config
 */
export const getConfig = (key) => {
  // First try environment variable
  const envKey = `REACT_APP_${key}`;
  const envValue = process.env[envKey];
  
  if (envValue && envValue !== 'undefined') {
    console.log(`✅ Config ${key}: Using environment variable (${envValue})`);
    return envValue;
  }
  
  // Fallback to production or development config
  const config = isNetlifyProduction() ? PRODUCTION_CONFIG : DEVELOPMENT_CONFIG;
  const configKey = key.replace('REACT_APP_', '');
  const configValue = config[configKey];
  
  if (configValue) {
    console.log(`⚠️ Config ${key}: Using fallback config (${configValue})`);
    return configValue;
  }
  
  console.error(`❌ Config ${key}: No value found in environment or fallback config`);
  return null;
};

/**
 * Get backend URL with intelligent fallback
 */
export const getBackendUrl = () => {
  // Try environment variable first
  const envBackendUrl = process.env.REACT_APP_BACKEND_URL;
  if (envBackendUrl && envBackendUrl !== 'undefined') {
    console.log('✅ Backend URL: Using environment variable:', envBackendUrl);
    return envBackendUrl;
  }
  
  // Fallback based on hostname
  if (isNetlifyProduction()) {
    console.log('⚠️ Backend URL: Using production fallback (Netlify detected)');
    return PRODUCTION_CONFIG.BACKEND_URL;
  } else {
    console.log('⚠️ Backend URL: Using development fallback (localhost detected)');
    return DEVELOPMENT_CONFIG.BACKEND_URL;
  }
};

/**
 * Get AWS configuration with fallbacks
 */
export const getAWSConfig = () => {
  return {
    identityPoolId: getConfig('AWS_IDENTITY_POOL_ID'),
    region: getConfig('AWS_REGION'),
    bucket: getConfig('S3_BUCKET')
  };
};

/**
 * Debug function to log all configuration
 */
export const debugConfig = () => {
  console.log('🔧 Configuration Debug:');
  console.log('  Environment:', isNetlifyProduction() ? 'PRODUCTION (Netlify)' : 'DEVELOPMENT');
  console.log('  Hostname:', window.location.hostname);
  console.log('  Backend URL:', getBackendUrl());
  console.log('  AWS Config:', getAWSConfig());
  console.log('  Environment Variables:');
  console.log('    REACT_APP_BACKEND_URL:', process.env.REACT_APP_BACKEND_URL);
  console.log('    REACT_APP_AWS_IDENTITY_POOL_ID:', process.env.REACT_APP_AWS_IDENTITY_POOL_ID);
  console.log('    REACT_APP_AWS_REGION:', process.env.REACT_APP_AWS_REGION);
  console.log('    REACT_APP_S3_BUCKET:', process.env.REACT_APP_S3_BUCKET);
};

// Export default configuration object
export default {
  getConfig,
  getBackendUrl,
  getAWSConfig,
  debugConfig,
  isNetlifyProduction
};
