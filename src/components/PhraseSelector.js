import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Button,
  Paper,
  List,
  ListItem,
  ListItemText,
  Checkbox,
  Divider,
  Alert,
  LinearProgress,
  Tooltip
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import RadioButtonUncheckedIcon from '@mui/icons-material/RadioButtonUnchecked';
import {
  getAllCategories,
  getPhrasesForCategory,
  getSelectedPhrases,
  getMaxPhrasesPerVolunteer,
  getPhraseCompletionData
} from '../services/phraseRotationService';
import { phraseCollectionConfig } from '../phrases';
import { useProgressTracking } from '../hooks/useProgressTracking';

const PhraseSelector = ({ onPhrasesSelected }) => {
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [availablePhrases, setAvailablePhrases] = useState([]);
  const [selectedPhrases, setSelectedPhrases] = useState([]);
  const [phraseCompletionData, setPhraseCompletionData] = useState({});
  const [completedPhrases, setCompletedPhrases] = useState([]);
  const maxPhrases = getMaxPhrasesPerVolunteer() || 999; // No limit if null, use high number as fallback
  const TOTAL_GOAL_PER_PHRASE = phraseCollectionConfig.recordingsPerPhrase; // Target recordings per phrase

  // Real-time progress tracking from S3
  const {
    progressData,
    isLoading: progressLoading,
    error: progressError,
    getPhraseProgress,
    getProgressString,
    isPhraseCompleted: isS3PhraseCompleted,
    refreshProgress,
    hasData: hasProgressData
  } = useProgressTracking({
    autoRefresh: true,
    refreshInterval: 60 * 60 * 1000, // 1 hour (optimised for performance)
    forceRefreshOnMount: true
  });

  // Initialize categories
  useEffect(() => {
    // Get all available categories
    const allCategories = getAllCategories();
    setCategories(allCategories);
    
    // Force refresh of phrases data
    console.log('Loading updated phrases:', new Date().toISOString());
    
    // Load completed phrases from localStorage (set when user clicks "Got Time For More")
    const completedPhrasesJson = localStorage.getItem('icuAppCompletedPhrases');
    if (completedPhrasesJson) {
      const parsedCompletedPhrases = JSON.parse(completedPhrasesJson);
      setCompletedPhrases(parsedCompletedPhrases);
      console.log('Loaded completed phrases:', parsedCompletedPhrases.length);
    }
    
    // Clear any pre-existing selected phrases to start fresh
    localStorage.removeItem('icuAppSelectedPhrases');
    setSelectedPhrases([]);
    console.log('Cleared previous phrase selections - starting fresh');
  }, []);

  // Update available phrases when category changes
  useEffect(() => {
    if (selectedCategory) {
      // Get all phrases for this category
      const allPhrases = getPhrasesForCategory(selectedCategory);
      console.log(`Loading phrases for category "${selectedCategory}":`, allPhrases);
      
      // CRITICAL FIX: Allow unlimited re-recording - do NOT filter out completed phrases
      // Keep all phrases available for selection regardless of completion status
      const filteredPhrases = allPhrases;
      
      console.log(`Filtered phrases for "${selectedCategory}":`, filteredPhrases);
      setAvailablePhrases(filteredPhrases);
      
      // Get phrase completion data (all set to 0 as requested)
      // In a real app, this would come from the backend
      const completionData = getPhraseCompletionData({});
      setPhraseCompletionData(completionData);
    } else {
      setAvailablePhrases([]);
      setPhraseCompletionData({});
    }
  }, [selectedCategory, completedPhrases]);

  const handleCategoryChange = (event) => {
    setSelectedCategory(event.target.value);
  };

  const handlePhraseToggle = (phrase) => {
    const currentIndex = selectedPhrases.findIndex(
      item => item.category === selectedCategory && item.phrase === phrase
    );

    const newSelectedPhrases = [...selectedPhrases];

    if (currentIndex === -1) {
      // Add the phrase (no maximum limit)
      newSelectedPhrases.push({
        category: selectedCategory,
        phrase
      });
    } else {
      // Remove the phrase
      newSelectedPhrases.splice(currentIndex, 1);
    }

    setSelectedPhrases(newSelectedPhrases);
  };

  const handleSelectAllToggle = () => {
    // CRITICAL FIX: Allow unlimited re-recording - include ALL phrases, even completed ones
    const availableUncompletedPhrases = availablePhrases.filter(phrase =>
      !isPhraseSelected(phrase)
    );

    if (availableUncompletedPhrases.length === 0) return;

    // If any phrase from this category is selected, deselect all
    const hasSelectedFromCategory = selectedPhrases.some(item => item.category === selectedCategory);

    const newSelectedPhrases = [...selectedPhrases];

    if (hasSelectedFromCategory) {
      // Remove all phrases from this category
      const filteredPhrases = newSelectedPhrases.filter(item => item.category !== selectedCategory);
      setSelectedPhrases(filteredPhrases);
    } else {
      // Add all available phrases from this category (no limit)
      availableUncompletedPhrases.forEach(phrase => {
        newSelectedPhrases.push({
          category: selectedCategory,
          phrase
        });
      });
      setSelectedPhrases(newSelectedPhrases);
    }
  };

  const isPhraseSelected = (phrase) => {
    return selectedPhrases.some(
      item => item.category === selectedCategory && item.phrase === phrase
    );
  };
  
  // Check if a phrase is already completed (has reached target recordings)
  const isPhraseCompleted = (phrase) => {
    // Use S3 data if available, fallback to local completion data
    if (hasProgressData) {
      return isS3PhraseCompleted(phrase);
    }

    // Fallback to local completion tracking
    return completedPhrases.some(
      item => item.category === selectedCategory && item.phrase === phrase
    );
  };

  // Get real-time progress count for a phrase
  const getPhraseCount = (phrase) => {
    if (hasProgressData) {
      const progress = getPhraseProgress(phrase);
      return progress ? progress.current : 0;
    }

    // Fallback to local data
    return phraseCompletionData[phrase] || 0;
  };

  // Calculate category completion status
  const getCategoryCompletionStatus = (category) => {
    const allPhrasesInCategory = getPhrasesForCategory(category);
    const completedPhrasesInCategory = completedPhrases.filter(
      item => item.category === category
    );

    const totalPhrases = allPhrasesInCategory.length;
    const completedCount = completedPhrasesInCategory.length;
    const completionPercentage = totalPhrases > 0 ? (completedCount / totalPhrases) * 100 : 0;

    return {
      totalPhrases,
      completedCount,
      completionPercentage,
      isFullyCompleted: completedCount === totalPhrases && totalPhrases > 0,
      isPartiallyCompleted: completedCount > 0 && completedCount < totalPhrases
    };
  };

  const handleRemovePhrase = (index) => {
    const newSelectedPhrases = [...selectedPhrases];
    newSelectedPhrases.splice(index, 1);
    setSelectedPhrases(newSelectedPhrases);
  };

  const handleSubmit = () => {
    if (selectedPhrases.length > 0) {
      onPhrasesSelected(selectedPhrases);
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 4, mt: 5 }}>
      <Typography variant="h5" gutterBottom>
        Pick a category to start
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Choose phrases that you would like to record.
        These phrases are important for ICU patients who cannot speak.
      </Typography>




      
      {completedPhrases.length > 0 && (
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2" sx={{ fontWeight: 'medium', mb: 1 }}>
            {completedPhrases.length} phrase(s) you've already completed have been filtered out:
          </Typography>
          <Box sx={{ maxHeight: 120, overflowY: 'auto', pl: 2 }}>
            {completedPhrases.map((item, index) => (
              <Typography key={index} variant="body2" sx={{ fontSize: '0.875rem', mb: 0.5 }}>
                • <strong>{item.category}:</strong> "{item.phrase}"
              </Typography>
            ))}
          </Box>
        </Alert>
      )}
      
      {/* Category selection */}
      <FormControl fullWidth sx={{ mb: 3 }}>
        <InputLabel id="category-select-label">Select Category</InputLabel>
        <Select
          labelId="category-select-label"
          id="category-select"
          value={selectedCategory}
          label="Select Category"
          onChange={handleCategoryChange}
        >
          {categories.map((category) => {
            const completionStatus = getCategoryCompletionStatus(category);
            return (
              <MenuItem
                key={category}
                value={category}
                sx={{
                  opacity: completionStatus.isFullyCompleted ? 0.6 : 1,
                  backgroundColor: completionStatus.isFullyCompleted
                    ? 'rgba(76, 175, 80, 0.1)'
                    : completionStatus.isPartiallyCompleted
                      ? 'rgba(255, 193, 7, 0.1)'
                      : 'transparent'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                  <Typography sx={{
                    textDecoration: completionStatus.isFullyCompleted ? 'line-through' : 'none',
                    color: completionStatus.isFullyCompleted
                      ? 'text.secondary'
                      : category.startsWith('Consumer trial word/phrases')
                        ? '#ff0000'
                        : 'text.primary'
                  }}>
                    {category}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
                    {completionStatus.isFullyCompleted && (
                      <CheckCircleIcon sx={{ color: 'success.main', fontSize: 20, mr: 1 }} />
                    )}
                    {completionStatus.isPartiallyCompleted && !completionStatus.isFullyCompleted && (
                      <Box sx={{
                        width: 16,
                        height: 16,
                        borderRadius: '50%',
                        backgroundColor: 'warning.main',
                        mr: 1,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}>
                        <Typography sx={{ color: 'white', fontSize: '10px', fontWeight: 'bold' }}>
                          {completionStatus.completedCount}
                        </Typography>
                      </Box>
                    )}
                    <Typography variant="caption" color="text.secondary">
                      {completionStatus.completedCount}/{completionStatus.totalPhrases}
                    </Typography>
                  </Box>
                </Box>
              </MenuItem>
            );
          })}
        </Select>
      </FormControl>
      
      {/* Available phrases */}
      {selectedCategory && (
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" color="primary">
              Available Phrases in "{selectedCategory}" ({availablePhrases.length} phrases)
            </Typography>
            {availablePhrases.length > 0 && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Checkbox
                  checked={selectedPhrases.some(item => item.category === selectedCategory)}
                  onChange={handleSelectAllToggle}
                />
                <Typography variant="body2">Select All</Typography>
              </Box>
            )}
          </Box>
          
          {availablePhrases.length === 0 ? (
            <Alert severity="info">
              No phrases available in this category.
            </Alert>
          ) : (
            <Paper variant="outlined" sx={{ border: '2px solid #e3f2fd' }}>
              <List dense>
                {availablePhrases.map((phrase, index) => {
                  const isSelected = isPhraseSelected(phrase);
                  const isCompleted = isPhraseCompleted(phrase);
                  const currentCount = getPhraseCount(phrase);
                  const isFullyCollected = currentCount >= TOTAL_GOAL_PER_PHRASE;
                  // CRITICAL FIX: Allow unlimited re-recording - never disable phrases based on completion
                  const isDisabled = false;
                  const progressPercentage = Math.min(100, (currentCount / TOTAL_GOAL_PER_PHRASE) * 100);

                  return (
                    <ListItem
                      key={index}
                      button
                      onClick={() => handlePhraseToggle(phrase)}
                      disabled={isDisabled}
                      sx={{
                        bgcolor: isSelected ? 'rgba(25, 118, 210, 0.12)' :
                                isCompleted ? 'rgba(76, 175, 80, 0.08)' : 'transparent',
                        textDecoration: isCompleted ? 'line-through' : 'none',
                        opacity: isCompleted ? 0.6 : 1,
                        borderBottom: '1px solid #f0f0f0',
                        '&:hover': {
                          bgcolor: isSelected ? 'rgba(25, 118, 210, 0.16)' : 'rgba(0, 0, 0, 0.04)'
                        }
                      }}
                    >
                      <Checkbox
                        edge="start"
                        checked={isSelected}
                        tabIndex={-1}
                        disableRipple
                      />
                      <ListItemText
                        primary={phrase}
                        primaryTypographyProps={{
                          variant: 'body1',
                          fontWeight: isSelected ? 'medium' : 'normal'
                        }}
                      />
                      {/* Real-time progress meter from S3 */}
                      <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
                        {isCompleted ? (
                          <CheckCircleIcon sx={{ color: 'success.main', mr: 1 }} />
                        ) : (
                          <RadioButtonUncheckedIcon sx={{ color: 'grey.400', mr: 1 }} />
                        )}
                        <Tooltip title={`${currentCount} recordings out of ${TOTAL_GOAL_PER_PHRASE} target`}>
                          <Box sx={{ width: 60, mr: 1, bgcolor: 'grey.300', borderRadius: 1, height: 8 }}>
                            <Box
                              sx={{
                                width: `${progressPercentage}%`,
                                bgcolor: isCompleted ? 'success.main' : 'primary.main',
                                height: 8,
                                borderRadius: 1,
                                transition: 'width 0.3s ease-in-out'
                              }}
                            />
                          </Box>
                        </Tooltip>
                        <Typography
                          variant="caption"
                          color={isCompleted ? 'success.main' : 'text.secondary'}
                          sx={{ fontWeight: isCompleted ? 'bold' : 'normal' }}
                        >
                          {`${currentCount}/${TOTAL_GOAL_PER_PHRASE}`}
                        </Typography>
                      </Box>
                    </ListItem>
                  );
                })}
              </List>
            </Paper>
          )}
        </Box>
      )}
      
      {/* Selected phrases */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Your Selected Phrases ({selectedPhrases.length})
        </Typography>
        {selectedPhrases.length === 0 ? (
          <Alert severity="info">
            No phrases selected yet. Please select phrases to continue.
          </Alert>
        ) : (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {selectedPhrases.map((item, index) => (
              <Chip
                key={index}
                label={`${item.phrase}`}
                onDelete={() => handleRemovePhrase(index)}
                color="primary"
                variant="outlined"
                sx={{ mb: 1 }}
              />
            ))}
          </Box>
        )}
      </Box>
      
      <Divider sx={{ my: 2 }} />
      
      <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          color="primary"
          disabled={selectedPhrases.length === 0}
          onClick={handleSubmit}
        >
          Continue with Selected Phrases
        </Button>
      </Box>
    </Paper>
  );
};

export default PhraseSelector;
