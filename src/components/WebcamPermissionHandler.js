import React, { useState, useEffect } from 'react';
import { <PERSON>, Button, Typography, Alert } from '@mui/material';
import VideocamIcon from '@mui/icons-material/Videocam';
import RefreshIcon from '@mui/icons-material/Refresh';

/**
 * WebcamPermissionHandler - A component to explicitly handle webcam permissions
 * This component provides a user-friendly interface for requesting camera permissions
 * and handling various error states with clear instructions.
 */
const WebcamPermissionHandler = ({ onPermissionGranted, onError }) => {
  const [permissionStatus, setPermissionStatus] = useState('initial'); // initial, checking, granted, denied, error
  const [errorDetails, setErrorDetails] = useState(null);
  const [isSecureContext, setIsSecureContext] = useState(true);

  // Comprehensive mobile browser and getUserMedia compatibility check
  useEffect(() => {
    // Comprehensive browser and device detection
    const userAgent = navigator.userAgent;
    const isIOS = /iPad|iPhone|iPod/.test(userAgent);
    const isAndroid = /Android/.test(userAgent);
    const isMobile = isIOS || isAndroid;
    const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
    const isChrome = /Chrome/.test(userAgent);
    const isFirefox = /Firefox/.test(userAgent);

    console.log('=== MOBILE CAMERA DIAGNOSTICS ===');
    console.log('User Agent:', userAgent);
    console.log('Device Detection:', { isIOS, isAndroid, isMobile, isSafari, isChrome, isFirefox });
    console.log('Window secure context:', window.isSecureContext);
    console.log('Current location:', window.location.href);
    console.log('Hostname:', window.location.hostname);
    console.log('Protocol:', window.location.protocol);

    // Enhanced secure context check for mobile development
    const isLocalNetwork = /^192\.168\.|^10\.|^172\.(1[6-9]|2[0-9]|3[01])\.|^localhost$|^127\.0\.0\.1$/.test(window.location.hostname);
    const secure = window.isSecureContext ||
                  window.location.hostname === 'localhost' ||
                  window.location.hostname === '127.0.0.1' ||
                  isLocalNetwork ||
                  window.location.protocol === 'https:';

    console.log('Is local network:', isLocalNetwork);
    console.log('Is secure context (calculated):', secure);

    // Comprehensive getUserMedia support detection
    console.log('=== GETUSERMEDIA SUPPORT DETECTION ===');
    console.log('navigator.mediaDevices exists:', !!navigator.mediaDevices);
    console.log('navigator.mediaDevices.getUserMedia exists:', !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia));
    console.log('navigator.getUserMedia exists:', !!navigator.getUserMedia);
    console.log('navigator.webkitGetUserMedia exists:', !!navigator.webkitGetUserMedia);
    console.log('navigator.mozGetUserMedia exists:', !!navigator.mozGetUserMedia);
    console.log('navigator.msGetUserMedia exists:', !!navigator.msGetUserMedia);

    setIsSecureContext(secure);

    if (!secure) {
      setErrorDetails('Camera access requires a secure context (HTTPS) or local network access. You appear to be using an insecure connection.');
      setPermissionStatus('error');
      return;
    }

    // Check for any form of getUserMedia support
    const hasModernGetUserMedia = navigator.mediaDevices && navigator.mediaDevices.getUserMedia;
    const hasLegacyGetUserMedia = navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia || navigator.msGetUserMedia;

    if (!hasModernGetUserMedia && !hasLegacyGetUserMedia) {
      setErrorDetails(`Your browser does not support camera access. Browser: ${isSafari ? 'Safari' : isChrome ? 'Chrome' : isFirefox ? 'Firefox' : 'Unknown'}. Please try a modern browser like Chrome or Safari.`);
      setPermissionStatus('error');
    }
  }, []);

  // Robust getUserMedia function with multiple fallbacks
  const getUserMediaWithFallbacks = async (constraints) => {
    console.log('=== ATTEMPTING GETUSERMEDIA WITH FALLBACKS ===');
    console.log('Requested constraints:', constraints);

    // Method 1: Modern navigator.mediaDevices.getUserMedia
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      console.log('Trying modern navigator.mediaDevices.getUserMedia...');
      try {
        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        console.log('✅ Modern getUserMedia succeeded');
        return stream;
      } catch (err) {
        console.log('❌ Modern getUserMedia failed:', err);
        // Continue to fallbacks
      }
    }

    // Method 2: Legacy getUserMedia with Promise wrapper
    const legacyGetUserMedia = navigator.getUserMedia ||
                              navigator.webkitGetUserMedia ||
                              navigator.mozGetUserMedia ||
                              navigator.msGetUserMedia;

    if (legacyGetUserMedia) {
      console.log('Trying legacy getUserMedia...');
      return new Promise((resolve, reject) => {
        legacyGetUserMedia.call(navigator, constraints,
          (stream) => {
            console.log('✅ Legacy getUserMedia succeeded');
            resolve(stream);
          },
          (err) => {
            console.log('❌ Legacy getUserMedia failed:', err);
            reject(err);
          }
        );
      });
    }

    // Method 3: Try with minimal constraints
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      console.log('Trying minimal constraints fallback...');
      try {
        const minimalConstraints = { video: true, audio: false };
        const stream = await navigator.mediaDevices.getUserMedia(minimalConstraints);
        console.log('✅ Minimal constraints succeeded');
        return stream;
      } catch (err) {
        console.log('❌ Minimal constraints failed:', err);
      }
    }

    throw new Error('getUserMedia is not supported or available in this browser');
  };

  // Function to request camera permission with comprehensive fallbacks
  const requestPermission = async () => {
    if (!isSecureContext) {
      setErrorDetails('Camera access requires a secure context (HTTPS) or local network access. You appear to be using an insecure connection.');
      setPermissionStatus('error');
      return;
    }

    try {
      setPermissionStatus('checking');
      console.log('=== REQUESTING CAMERA PERMISSION ===');
      console.log('User agent:', navigator.userAgent);

      // Mobile-optimized camera constraints
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
      const videoConstraints = isMobile ? {
        video: {
          facingMode: 'user',
          width: { ideal: 640, max: 1280 },
          height: { ideal: 480, max: 720 }
        },
        audio: false
      } : {
        video: true,
        audio: false
      };

      console.log('Using video constraints:', videoConstraints);

      // Use our robust fallback system
      const stream = await getUserMediaWithFallbacks(videoConstraints);

      console.log('✅ Camera permission granted', stream);
      console.log('Stream tracks:', stream.getTracks().map(track => ({
        kind: track.kind,
        label: track.label,
        enabled: track.enabled,
        readyState: track.readyState
      })));

      // Stop the stream - we just wanted to get permission
      // The parent component will create its own stream
      stream.getTracks().forEach(track => track.stop());

      setPermissionStatus('granted');

      // Notify parent component
      if (onPermissionGranted) {
        onPermissionGranted();
      }
    } catch (err) {
      console.error('Camera permission error:', err);
      // Log detailed error information
      console.log('Error name:', err.name);
      console.log('Error message:', err.message);
      console.log('Error object:', JSON.stringify(err, Object.getOwnPropertyNames(err)));
      console.log('Browser:', navigator.userAgent);
      
      // Handle specific error types with detailed messages and mobile-specific troubleshooting
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

      if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
        const mobileGuidance = isMobile ?
          ' On mobile: Check your browser app permissions in device settings, then refresh this page.' :
          '';
        setErrorDetails(`Camera access denied. Please allow camera access in your browser settings and try again.${mobileGuidance}`);
        setPermissionStatus('denied');
      } else if (err.name === 'NotFoundError') {
        setErrorDetails('No camera found. Please ensure your device has a camera and try refreshing the page.');
        setPermissionStatus('error');
      } else if (err.name === 'NotReadableError') {
        const mobileGuidance = isMobile ?
          ' On mobile: Close other camera apps and try again.' :
          '';
        setErrorDetails(`Camera is already in use by another application.${mobileGuidance} Please close other apps using your camera and try again.`);
        setPermissionStatus('error');
      } else if (err.name === 'AbortError') {
        setErrorDetails('Camera access request was aborted. This may be due to a hardware error or system limitation.');
        setPermissionStatus('error');
      } else if (err.name === 'SecurityError') {
        setErrorDetails('Camera access was blocked due to security restrictions. Make sure you are using HTTPS, localhost, or a local network connection.');
        setPermissionStatus('error');
      } else if (err.name === 'OverconstrainedError') {
        setErrorDetails('Camera constraints cannot be satisfied. Your device camera may not support the required settings.');
        setPermissionStatus('error');
      } else if (err.message && err.message.includes('getUserMedia is not implemented')) {
        setErrorDetails('Camera access is not supported in this browser. Please try using Chrome, Safari, or Firefox on your mobile device.');
        setPermissionStatus('error');
      } else {
        setErrorDetails(`Camera error: ${err.name || 'Unknown error'} - ${err.message || 'No details available'}`);
        setPermissionStatus('error');
      }
      
      // Notify parent component of error
      if (onError) {
        onError(err);
      }
    }
  };

  // Render different UI based on permission status
  const renderContent = () => {
    switch (permissionStatus) {
      case 'initial':
        return (
          <Box sx={{ textAlign: 'center', p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Camera Permission Required
            </Typography>
            <Typography variant="body1" sx={{ mb: 2 }}>
              This application needs access to your camera to record videos.
              When prompted by your browser, please click "Allow" to grant camera access.
            </Typography>
            <Alert severity="info" sx={{ mb: 3, textAlign: 'left' }}>
              <Typography variant="body2" component="div">
                <strong>Tips for Mobile Devices:</strong>
                <ul style={{ paddingLeft: '20px', marginTop: '5px', marginBottom: '5px' }}>
                  <li><strong>iPhone/iPad:</strong> Use Safari browser for best compatibility</li>
                  <li><strong>Android:</strong> Use Chrome browser for best compatibility</li>
                  <li>Make sure no other camera apps are running</li>
                  <li>Check your browser app permissions in device settings</li>
                  <li>If prompted, tap "Allow" when asked for camera access</li>
                </ul>
              </Typography>
            </Alert>
            <Button
              variant="contained"
              color="primary"
              startIcon={<VideocamIcon />}
              onClick={requestPermission}
              size="large"
              sx={{ minWidth: '240px' }}
            >
              Request Camera Permission
            </Button>
          </Box>
        );
        
      case 'checking':
        return (
          <Box sx={{ textAlign: 'center', p: 3 }}>
            <Typography variant="h6">
              Requesting Camera Permission...
            </Typography>
            <Typography variant="body2" sx={{ mt: 1 }}>
              Please respond to the browser's permission prompt.
            </Typography>
          </Box>
        );
        
      case 'granted':
        return (
          <Box sx={{ textAlign: 'center', p: 2 }}>
            <Alert severity="success">
              Camera permission granted! You can now record videos.
            </Alert>
          </Box>
        );
        
      case 'denied':
      case 'error':
        return (
          <Box sx={{ textAlign: 'center', p: 3 }}>
            <Alert severity="error" sx={{ mb: 3 }}>
              <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 1 }}>
                Camera Access Problem
              </Typography>
              <Typography variant="body2">
                {errorDetails}
              </Typography>
            </Alert>
            
            {permissionStatus === 'denied' && (
              <Box sx={{ mt: 2, mb: 3, bgcolor: '#f5f5f5', p: 2, borderRadius: 1 }}>
                <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
                  How to Fix Permission Issues:
                </Typography>
                <Typography variant="body2" component="div" sx={{ textAlign: 'left' }}>
                  <ol style={{ paddingLeft: '20px', margin: '8px 0' }}>
                    <li>Click the padlock/info icon in your browser's address bar</li>
                    <li>Look for camera settings and change to "Allow"</li>
                    <li>If you don't see this option, try clearing site data and refreshing</li>
                    <li>On mobile devices, check your browser app permissions in device settings</li>
                  </ol>
                </Typography>
              </Box>
            )}
            
            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 2 }}>
              <Button
                variant="contained"
                color="primary"
                onClick={requestPermission}
                startIcon={<VideocamIcon />}
                sx={{ minWidth: '140px' }}
              >
                Try Again
              </Button>
              
              <Button
                variant="outlined"
                onClick={() => window.location.reload()}
                startIcon={<RefreshIcon />}
                sx={{ minWidth: '140px' }}
              >
                Refresh Page
              </Button>
            </Box>
          </Box>
        );
        
      default:
        return null;
    }
  };

  return (
    <Box sx={{ 
      maxWidth: 600, 
      mx: 'auto', 
      my: 2,
      border: '1px solid #e0e0e0',
      borderRadius: 2,
      bgcolor: '#f9f9f9'
    }}>
      {renderContent()}
    </Box>
  );
};

export default WebcamPermissionHandler;
