// Environment Variable Test for AWS Storage
console.log('🔍 ENVIRONMENT VARIABLE TEST AT MODULE LOAD TIME');
console.log('================================================');

console.log('📊 Environment Variables at Module Load:');
console.log('  - REACT_APP_AWS_IDENTITY_POOL_ID:', process.env.REACT_APP_AWS_IDENTITY_POOL_ID ? 'SET (' + process.env.REACT_APP_AWS_IDENTITY_POOL_ID.substring(0, 20) + '...)' : 'NOT SET');
console.log('  - REACT_APP_AWS_REGION:', process.env.REACT_APP_AWS_REGION || 'NOT SET');
console.log('  - REACT_APP_S3_BUCKET:', process.env.REACT_APP_S3_BUCKET || 'NOT SET');
console.log('  - NODE_ENV:', process.env.NODE_ENV || 'NOT SET');

// Test isAWSConfigured logic
const hasIdentityPool = !!process.env.REACT_APP_AWS_IDENTITY_POOL_ID;
const hasRegion = !!process.env.REACT_APP_AWS_REGION;
const hasBucket = !!process.env.REACT_APP_S3_BUCKET;
const forceBackendMode = false;

const isConfigured = hasIdentityPool && hasRegion && hasBucket && !forceBackendMode;

console.log('🔧 Configuration Check:');
console.log('  - hasIdentityPool:', hasIdentityPool);
console.log('  - hasRegion:', hasRegion);
console.log('  - hasBucket:', hasBucket);
console.log('  - forceBackendMode:', forceBackendMode);
console.log('  - isConfigured:', isConfigured);

// Test S3 client initialization
let s3ClientTest = null;
if (isConfigured) {
  try {
    console.log('🚀 Attempting S3 client initialization...');
    
    // Import AWS SDK
    import('@aws-sdk/client-s3').then(({ S3Client }) => {
      import('@aws-sdk/credential-providers').then(({ fromCognitoIdentityPool }) => {
        try {
          s3ClientTest = new S3Client({
            region: process.env.REACT_APP_AWS_REGION,
            credentials: fromCognitoIdentityPool({
              clientConfig: { region: process.env.REACT_APP_AWS_REGION },
              identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID
            })
          });
          console.log('✅ S3 client initialized successfully in test');
        } catch (initError) {
          console.error('❌ S3 client initialization failed in test:', initError);
        }
      }).catch(credError => {
        console.error('❌ Failed to import credential providers:', credError);
      });
    }).catch(s3Error => {
      console.error('❌ Failed to import S3Client:', s3Error);
    });
    
  } catch (error) {
    console.error('❌ S3 client test failed:', error);
  }
} else {
  console.log('⚠️ AWS not configured - S3 client will not be initialized');
}

export const envTestResults = {
  hasIdentityPool,
  hasRegion,
  hasBucket,
  isConfigured,
  s3ClientTest
};
