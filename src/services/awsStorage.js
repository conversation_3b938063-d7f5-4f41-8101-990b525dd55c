import { S3<PERSON><PERSON>, PutObjectCommand, HeadO<PERSON>Command, GetObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { fromCognitoIdentityPool } from '@aws-sdk/credential-providers';
import { getBackendUrl, getAWSConfig, debugConfig } from '../config/production';

const BUCKET_NAME = process.env.REACT_APP_S3_BUCKET || 'icudatasetphrasesfortesting';
const REGION = process.env.REACT_APP_AWS_REGION || 'ap-southeast-2';

// Check if AWS credentials are properly configured
const isAWSConfigured = () => {
  const hasIdentityPool = !!process.env.REACT_APP_AWS_IDENTITY_POOL_ID;
  const hasRegion = !!process.env.REACT_APP_AWS_REGION;
  const hasBucket = !!process.env.REACT_APP_S3_BUCKET;

  // CORS issues detected - force backend upload mode for reliability
  // Set this to true to force backend upload mode (recommended for production)
  const forceBackendMode = true;

  const isConfigured = hasIdentityPool && hasRegion && hasBucket && !forceBackendMode;

  if (forceBackendMode) {
    console.log('🔄 FORCED BACKEND MODE: Using backend upload (forceBackendMode = true)');
    console.log('🔧 AWS credentials available but backend mode forced:', {
      hasIdentityPool,
      hasRegion,
      hasBucket,
      forceBackendMode: true
    });
  } else if (isConfigured) {
    console.log('✅ AWS configured for frontend direct upload mode');
    console.log('🔧 AWS Config:', {
      identityPool: process.env.REACT_APP_AWS_IDENTITY_POOL_ID?.substring(0, 20) + '...',
      region: process.env.REACT_APP_AWS_REGION,
      bucket: process.env.REACT_APP_S3_BUCKET
    });
  } else {
    console.log('⚠️ AWS not fully configured, will attempt backend upload');
    console.log('🔧 Missing:', {
      identityPool: !hasIdentityPool,
      region: !hasRegion,
      bucket: !hasBucket
    });
  }

  return isConfigured;
};

let s3Client = null;
let s3ClientInitialized = false;

// Lazy initialization function for S3 client
const initializeS3Client = () => {
  if (s3ClientInitialized) {
    return s3Client;
  }

  console.log('🔧 Initializing S3 client (lazy initialization)...');

  if (!isAWSConfigured()) {
    console.warn('⚠️ AWS credentials not configured. S3 uploads will use backend mode.');
    s3ClientInitialized = true;
    return null;
  }

  try {
    console.log('🚀 Creating S3 client with credentials...');
    s3Client = new S3Client({
      region: REGION,
      credentials: fromCognitoIdentityPool({
        clientConfig: { region: REGION },
        identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID
      })
    });
    console.log('✅ AWS S3 client initialized successfully');
    s3ClientInitialized = true;
    return s3Client;
  } catch (error) {
    console.error('❌ Failed to initialize AWS S3 client:', error);
    console.error('🔧 Error details:', {
      name: error.name,
      message: error.message,
      identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID ? 'SET' : 'MISSING',
      region: REGION
    });
    s3Client = null;
    s3ClientInitialized = true;
    return null;
  }
};

const sanitizeToAscii = (str) => {
  // Convert to lowercase and replace spaces/punctuation with underscores
  return str
    .toLowerCase()
    .normalize('NFKD') // Decompose accented characters
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
    .replace(/[^a-z0-9_]/g, '_') // Replace non-alphanumeric with underscore
    .replace(/_+/g, '_') // Replace multiple underscores with single
    .replace(/^_|_$/g, ''); // Remove leading/trailing underscores
};

const formatTimestamp = (timestamp) => {
  // Format: YYYYMMDDTHHMMSS
  return timestamp
    .replace(/[-:]/g, '')
    .replace(/\..+/, '')
    .replace(' ', 'T');
};

const generateFilename = (phrase, userId, ageGroup, gender, ethnicity, timestamp) => {
  // Validate and normalize components
  const validAgeGroups = ['18to39', '40to64', '65plus'];
  const validGenders = ['male', 'female', 'nonbinary'];
  const validEthnicities = [
    'asian',
    'caucasian',
    'aboriginal',
    'african',
    'latinx',
    'middle_eastern',
    'pacific_islander',
    'indigenous'
  ];
  
  // Normalize inputs
  const normalizedAgeGroup = validAgeGroups.find(ag => ag === ageGroup?.toLowerCase()) || '40to64';
  const normalizedGender = validGenders.find(g => g === gender?.toLowerCase()) || 'female';
  const normalizedEthnicity = validEthnicities.find(e => e === ethnicity?.toLowerCase()) || 'not_specified';
  
  // Format components according to spec
  const components = [
    sanitizeToAscii(phrase),
    `user${String(userId).padStart(2, '0')}`,
    normalizedAgeGroup,
    normalizedGender,
    normalizedEthnicity,
    formatTimestamp(timestamp)
  ];
  
  return components.join('__') + '.webm'; // Changed to .webm to match actual video format
};

// Test AWS credentials and bucket access
export const testAWSConnection = async () => {
  console.log('🧪 Testing AWS connection...');
  console.log('🔍 Environment check:', {
    identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
    region: process.env.REACT_APP_AWS_REGION,
    bucket: process.env.REACT_APP_S3_BUCKET,
    isAWSConfigured: isAWSConfigured()
  });

  if (!s3Client) {
    console.error('❌ S3 client not initialized');
    console.error('🔍 Initialization check:', {
      isAWSConfigured: isAWSConfigured(),
      identityPoolId: process.env.REACT_APP_AWS_IDENTITY_POOL_ID,
      region: REGION
    });
    return { success: false, error: 'S3 client not initialized - check AWS configuration' };
  }

  try {
    console.log('🔍 Testing bucket access...');
    console.log('🔍 About to send ListObjectsV2Command to bucket:', BUCKET_NAME);

    const listCommand = new ListObjectsV2Command({
      Bucket: BUCKET_NAME,
      MaxKeys: 1
    });

    console.log('⏳ Sending AWS request...');
    const result = await s3Client.send(listCommand);
    console.log('✅ AWS connection test successful!');
    console.log('📊 Bucket access confirmed:', {
      bucket: BUCKET_NAME,
      region: REGION,
      objectCount: result.KeyCount || 0,
      contents: result.Contents?.map(obj => ({ key: obj.Key, size: obj.Size })) || []
    });

    return {
      success: true,
      bucket: BUCKET_NAME,
      region: REGION,
      objectCount: result.KeyCount || 0,
      sampleFiles: result.Contents?.slice(0, 3) || []
    };
  } catch (error) {
    console.error('❌ AWS connection test failed:', error);
    console.error('📋 Full error details:', {
      name: error.name,
      message: error.message,
      code: error.code,
      statusCode: error.$metadata?.httpStatusCode,
      requestId: error.$metadata?.requestId,
      region: error.$metadata?.region,
      service: error.$metadata?.service,
      stack: error.stack?.split('\n').slice(0, 3).join('\n')
    });

    // Enhanced error analysis
    let enhancedError = error.message;
    if (error.name === 'CredentialsProviderError') {
      enhancedError = 'AWS Cognito Identity Pool credentials failed. Check if the Identity Pool exists and has proper permissions.';
    } else if (error.message?.includes('Failed to fetch')) {
      enhancedError = 'Network error: Unable to reach AWS services. This could be a CORS issue, network connectivity problem, or invalid AWS region.';
    } else if (error.name === 'AccessDenied') {
      enhancedError = 'Access denied to S3 bucket. Check if the Cognito Identity Pool has S3 permissions.';
    } else if (error.name === 'NoSuchBucket') {
      enhancedError = `S3 bucket '${BUCKET_NAME}' does not exist in region '${REGION}'.`;
    }

    return {
      success: false,
      error: enhancedError,
      originalError: error.message,
      errorCode: error.name,
      statusCode: error.$metadata?.httpStatusCode,
      requestId: error.$metadata?.requestId,
      fullError: error
    };
  }
};

// Test S3 upload with a small test file
export const testS3Upload = async () => {
  console.log('🧪 Testing S3 upload with test file...');

  if (!s3Client) {
    console.error('❌ S3 client not initialized for upload test');
    return { success: false, error: 'S3 client not initialized' };
  }

  try {
    // Create a small test blob
    const testData = new Blob(['Test upload from ICU Dataset Application'], { type: 'text/plain' });
    const testKey = `test-uploads/connection-test-${Date.now()}.txt`;

    console.log('🔄 Converting test blob to ArrayBuffer...');
    const testArrayBuffer = await testData.arrayBuffer();

    console.log('🔄 Uploading test file...');
    const uploadParams = {
      Bucket: BUCKET_NAME,
      Key: testKey,
      Body: testArrayBuffer, // Use ArrayBuffer instead of Blob
      ContentType: 'text/plain',
      Metadata: {
        'test-upload': 'true',
        'timestamp': new Date().toISOString(),
        'app-version': '1.0.0'
      }
    };

    const uploadResult = await s3Client.send(new PutObjectCommand(uploadParams));
    console.log('✅ Test upload successful!');
    console.log('📊 Upload result:', uploadResult);

    // Verify the file exists
    const headResult = await s3Client.send(new HeadObjectCommand({
      Bucket: BUCKET_NAME,
      Key: testKey
    }));

    console.log('✅ Test file verified in bucket!');
    console.log('📋 File metadata:', headResult.Metadata);

    return {
      success: true,
      uploadResult,
      fileUrl: `https://${BUCKET_NAME}.s3.${REGION}.amazonaws.com/${testKey}`,
      metadata: headResult.Metadata
    };

  } catch (error) {
    console.error('❌ Test upload failed:', error);
    console.error('📋 Upload error details:', {
      name: error.name,
      message: error.message,
      code: error.code,
      statusCode: error.$metadata?.httpStatusCode,
      requestId: error.$metadata?.requestId
    });

    return {
      success: false,
      error: error.message,
      errorCode: error.name,
      statusCode: error.$metadata?.httpStatusCode,
      fullError: error
    };
  }
};

const generateS3Key = (filename, ageGroup, gender, ethnicity, phrase) => {
  // Ensure all path components are properly sanitized
  const sanitizedPhrase = sanitizeToAscii(phrase);
  
  // Use normalized values from filename generation for consistency
  const validAgeGroups = ['18to39', '40to64', '65plus'];
  const validGenders = ['male', 'female', 'nonbinary'];
  const validEthnicities = [
    'asian',
    'caucasian',
    'aboriginal',
    'african',
    'latinx',
    'middle_eastern',
    'pacific_islander',
    'indigenous'
  ];
  
  const normalizedAgeGroup = validAgeGroups.find(ag => ag === ageGroup?.toLowerCase()) || '40to64';
  const normalizedGender = validGenders.find(g => g === gender?.toLowerCase()) || 'female';
  const normalizedEthnicity = validEthnicities.find(e => e === ethnicity?.toLowerCase()) || 'not_specified';
  
  return `icu-videos/${normalizedAgeGroup}/${normalizedGender}/${normalizedEthnicity}/${sanitizedPhrase}/${filename}`;
};

export const uploadVideoToS3 = async (videoBlob, phrase, demographics, recordingNumber = 1, options = {}) => {
  try {
    console.log('=== AWS STORAGE: Starting upload process ===');
    console.log('🔍 Environment Variables Check:');
    console.log('  - REACT_APP_AWS_IDENTITY_POOL_ID:', process.env.REACT_APP_AWS_IDENTITY_POOL_ID);
    console.log('  - REACT_APP_AWS_REGION:', process.env.REACT_APP_AWS_REGION);
    console.log('  - REACT_APP_S3_BUCKET:', process.env.REACT_APP_S3_BUCKET);
    console.log('  - REACT_APP_BACKEND_URL:', process.env.REACT_APP_BACKEND_URL);
    console.log('  - isAWSConfigured():', isAWSConfigured());
    console.log('  - s3Client initialized (old):', !!s3Client);
    console.log('  - s3Client initialized (lazy):', !!initializeS3Client());

    console.log('📊 Input parameters validation:');
    console.log('  - videoBlobSize:', videoBlob.size);
    console.log('  - videoBlobType:', videoBlob.type);
    console.log('  - phrase:', phrase);
    console.log('  - demographics:', demographics);

    // Validate input parameters
    if (!videoBlob || videoBlob.size === 0) {
      throw new Error('VALIDATION_ERROR: Invalid video blob provided');
    }

    if (!phrase || typeof phrase !== 'string') {
      throw new Error('VALIDATION_ERROR: Invalid phrase provided');
    }

    if (!demographics || typeof demographics !== 'object') {
      throw new Error('VALIDATION_ERROR: Invalid demographics provided');
    }

    console.log('✅ Input validation passed');

    // Enhanced input validation with detailed error messages
    if (!videoBlob) {
      const error = new Error('VALIDATION_ERROR: Video blob is null or undefined');
      console.error('❌ Validation failed:', error.message);
      throw error;
    }
    if (videoBlob.size === 0) {
      const error = new Error('VALIDATION_ERROR: Video blob is empty (0 bytes)');
      console.error('❌ Validation failed:', error.message);
      throw error;
    }
    if (videoBlob.size > 50 * 1024 * 1024) { // 50MB limit
      const error = new Error(`VALIDATION_ERROR: Video blob too large (${Math.round(videoBlob.size / 1024 / 1024)}MB). Maximum size is 50MB.`);
      console.error('❌ Validation failed:', error.message);
      throw error;
    }
    if (!phrase || phrase.trim() === '') {
      const error = new Error('VALIDATION_ERROR: Phrase is empty or null');
      console.error('❌ Validation failed:', error.message);
      throw error;
    }
    if (!demographics) {
      const error = new Error('VALIDATION_ERROR: Demographics object is required');
      console.error('❌ Validation failed:', error.message);
      throw error;
    }

    console.log('✅ Input validation passed');

    const timestamp = new Date().toISOString();
    console.log('Generated timestamp:', timestamp);

    let filename = generateFilename(
      phrase,
      demographics.userId || 'user01',
      demographics.ageGroup || '40to64',
      demographics.gender || 'female',
      demographics.ethnicity || 'not_specified',
      timestamp
    );

    // Apply LipNet-specific filename convention if provided
    if (options.filenameSuffix === '_lipnet') {
      // LipNet filename convention: [phrase_label]_[user_id]_[timestamp]_lipnet.mp4
      const sanitizedPhrase = sanitizeToAscii(phrase);
      const userId = demographics.userId || 'user01';
      const lipnetTimestamp = formatTimestamp(timestamp);
      filename = `${sanitizedPhrase}_${userId}_${lipnetTimestamp}_lipnet.webm`;
      console.log('🎨 LipNet filename convention applied:', filename);
    } else if (options.filenameSuffix) {
      // Standard suffix application for other cases
      const extension = filename.substring(filename.lastIndexOf('.'));
      const baseName = filename.substring(0, filename.lastIndexOf('.'));
      filename = baseName + options.filenameSuffix + extension;
    }

    console.log('Generated filename:', filename);

    // CRITICAL FIX: Initialize S3 client lazily and determine upload mode
    const currentS3Client = initializeS3Client();
    const shouldUseBackend = !currentS3Client || !isAWSConfigured();

    if (shouldUseBackend) {
      console.log('=== AWS STORAGE: Using backend upload mode ===');
      console.log('🔄 BACKEND UPLOAD MODE: Sending to server for processing');
      console.log('🔧 Backend mode reason:', {
        s3ClientExists: !!currentS3Client,
        isAWSConfigured: isAWSConfigured(),
        reason: !currentS3Client ? 'S3 client not initialized' : 'AWS not configured'
      });

      try {
        console.log('📤 Preparing FormData for backend upload...');
        const formData = new FormData();
        // Use proper filename with privacy compliance indicators
        const privacyCompliantFilename = `${phrase}_mouth_only_${Date.now()}.webm`;
        formData.append('video', videoBlob, privacyCompliantFilename);
        formData.append('phrase', phrase);
        formData.append('category', demographics.category || 'general');
        formData.append('recordingNumber', recordingNumber?.toString() || '1');
        formData.append('demographics', JSON.stringify(demographics));

        console.log('🌐 Sending upload request to backend...');

        // Use production-aware configuration
        debugConfig(); // Log configuration details
        const backendUrl = getBackendUrl();
        const uploadUrl = `${backendUrl}/upload`;

        console.log('📍 Backend upload URL:', uploadUrl);
        console.log('🔧 Environment check:', {
          REACT_APP_BACKEND_URL: process.env.REACT_APP_BACKEND_URL,
          backendUrl,
          uploadUrl,
          hostname: window.location.hostname,
          isProduction: window.location.hostname === 'icuphrasecollection.com'
        });

        console.log('📤 Sending to backend server...');
        console.log('🔧 Backend request details:', {
          url: uploadUrl,
          method: 'POST',
          formDataKeys: Array.from(formData.keys()),
          videoSize: videoBlob.size,
          videoType: videoBlob.type
        });

        const response = await fetch(uploadUrl, {
          method: 'POST',
          body: formData,
          // Add headers for better debugging
          headers: {
            'X-Upload-Source': 'ICU-Dataset-App',
            'X-Upload-Mode': 'Backend-Fallback'
          }
        });

        console.log('📥 Backend response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('❌ Backend upload failed:', errorText);
          throw new Error(`Backend upload failed: ${response.status} - ${errorText}`);
        }

        const result = await response.json();
        console.log('✅ Backend upload successful:', result);

        return {
          url: result.url,
          key: result.filePath,
          filename: result.blobName,
          backendUpload: true,
          success: result.success
        };

      } catch (error) {
        console.error('❌ Backend upload error:', error);
        console.error('🔧 Backend upload troubleshooting:', {
          backendUrl: process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000',
          errorType: error.name,
          errorMessage: error.message,
          networkError: error.message?.includes('fetch') || error.message?.includes('network')
        });

        // Provide specific error messages for common network issues
        if (error.message.includes('fetch') || error.name === 'TypeError') {
          throw new Error('BACKEND_CONNECTION_ERROR: Unable to connect to backend server. Please ensure the server is running on http://localhost:5000');
        } else if (error.message.includes('Failed to fetch')) {
          throw new Error('BACKEND_NETWORK_ERROR: Network error connecting to backend. Please check your internet connection and server status.');
        } else if (error.message.includes('NetworkError')) {
          throw new Error('BACKEND_NETWORK_ERROR: Network error occurred during backend upload. Please try again.');
        } else if (error.message.includes('500')) {
          throw new Error('BACKEND_SERVER_ERROR: Backend server error. Please check server logs for details.');
        } else {
          throw new Error(`BACKEND_UPLOAD_ERROR: Backend upload failed - ${error.message}`);
        }
      }
    }

    console.log('🔄 Generating S3 key...');
    const s3Key = generateS3Key(
      filename,
      demographics.ageGroup || '40to64',
      demographics.gender || 'female',
      demographics.ethnicity || 'not_specified',
      phrase
    );
    console.log('✅ Generated S3 key:', s3Key);

    console.log('🔄 Converting blob to ArrayBuffer for AWS SDK compatibility...');
    // Convert blob to ArrayBuffer to avoid streaming issues
    const arrayBuffer = await videoBlob.arrayBuffer();
    console.log('✅ Blob converted to ArrayBuffer:', {
      originalBlobSize: videoBlob.size,
      arrayBufferSize: arrayBuffer.byteLength
    });

    console.log('🔄 Preparing upload parameters...');
    // Determine content type based on video blob type
    let contentType = 'video/webm'; // Default
    if (videoBlob.type) {
      contentType = videoBlob.type;
      console.log('✅ Using video blob content type:', contentType);
    } else {
      console.log('⚠️ Video blob has no type, using default:', contentType);
    }

    const uploadParams = {
      Bucket: BUCKET_NAME,
      Key: s3Key,
      Body: arrayBuffer, // Use ArrayBuffer instead of Blob to avoid streaming issues
      ContentType: contentType, // Use actual video content type
      Metadata: {
        phrase,
        userId: demographics.userId || 'user01',
        ageGroup: demographics.ageGroup || '40to64',
        gender: demographics.gender || 'female',
        ethnicity: demographics.ethnicity || 'not_specified',
        timestamp,
        recordingNumber: recordingNumber?.toString() || '1',
        category: demographics.category || 'general',
        // Privacy compliance metadata
        privacy_compliant: 'true',
        mouth_only_recording: 'true',
        excludes_eyes: 'true',
        lipnet_compatible: 'true',
        video_dimensions: '150x75',
        aspect_ratio: '2:1',
        // LipNet-specific metadata fields
        ...(options.filenameSuffix === '_lipnet' && {
          lipnet_preprocessed: 'true',
          mouth_roi_detected: options.mouthDetected ? 'true' : 'false',
          landmark_confidence: options.landmarkConfidence?.toString() || '0.0',
          processing_method: 'realtime_preprocessing',
          video_format: 'lipnet_optimized'
        })
      }
    };
    console.log('✅ Upload parameters prepared:', {
      bucket: uploadParams.Bucket,
      key: uploadParams.Key,
      contentType: uploadParams.ContentType,
      bodySize: uploadParams.Body.byteLength,
      metadataKeys: Object.keys(uploadParams.Metadata)
    });

    console.log('🚀 Starting S3 upload...');
    console.log('🔍 About to send PutObjectCommand with params:', {
      bucket: uploadParams.Bucket,
      key: uploadParams.Key,
      contentType: uploadParams.ContentType,
      bodySize: uploadParams.Body.byteLength
    });

    try {
      console.log('⏳ Sending upload request to S3...');
      const uploadResult = await currentS3Client.send(new PutObjectCommand(uploadParams));
      console.log('✅ S3 upload completed successfully!');
      console.log('📊 Upload result details:', {
        ETag: uploadResult.ETag,
        VersionId: uploadResult.VersionId,
        ServerSideEncryption: uploadResult.ServerSideEncryption,
        httpStatusCode: uploadResult.$metadata?.httpStatusCode,
        requestId: uploadResult.$metadata?.requestId
      });
      console.log('🌐 File should be available at:', `https://${BUCKET_NAME}.s3.${REGION}.amazonaws.com/${s3Key}`);
    } catch (uploadError) {
      console.error('❌ S3 upload failed with error:', uploadError);
      console.error('📋 Detailed error information:', {
        name: uploadError.name,
        message: uploadError.message,
        code: uploadError.code,
        statusCode: uploadError.$metadata?.httpStatusCode,
        requestId: uploadError.$metadata?.requestId,
        region: uploadError.$metadata?.region,
        service: uploadError.$metadata?.service,
        attempts: uploadError.$metadata?.attempts
      });

      // Enhanced error handling with specific error types
      if (uploadError.name === 'CredentialsProviderError') {
        throw new Error('AWS_CREDENTIALS_ERROR: AWS credentials not configured properly. Please check your environment variables.');
      } else if (uploadError.name === 'AccessDenied' || uploadError.code === 'AccessDenied') {
        throw new Error('AWS_ACCESS_DENIED: Access denied to S3 bucket. Please check bucket permissions and IAM policies.');
      } else if (uploadError.name === 'NoSuchBucket' || uploadError.code === 'NoSuchBucket') {
        throw new Error(`AWS_BUCKET_NOT_FOUND: S3 bucket '${BUCKET_NAME}' does not exist in region '${REGION}'.`);
      } else if (uploadError.name === 'NetworkingError' || uploadError.message?.includes('network')) {
        throw new Error('AWS_NETWORK_ERROR: Network error during upload. Please check your internet connection and try again.');
      } else if (uploadError.code === 'EntityTooLarge') {
        throw new Error('AWS_FILE_TOO_LARGE: Video file is too large for S3 upload.');
      } else {
        throw new Error(`AWS_UPLOAD_ERROR: S3 upload failed - ${uploadError.message}`);
      }
    }

    console.log('🔄 Updating metadata manifest...');
    // Update metadata manifest
    try {
      await updateMetadataManifest({
        filename,
        phrase,
        ...demographics,
        timestamp
      });
      console.log('✅ Metadata manifest updated successfully');
    } catch (manifestError) {
      console.warn('⚠️ Metadata manifest update failed (non-critical):', manifestError);
      // Don't fail the upload if manifest update fails
    }

    const finalResult = {
      url: `https://${BUCKET_NAME}.s3.${REGION}.amazonaws.com/${s3Key}`,
      key: s3Key,
      filename
    };

    console.log('✅ AWS STORAGE: Upload process completed successfully!');
    console.log('📊 Final result:', finalResult);

    return finalResult;
  } catch (error) {
    console.error('❌ AWS STORAGE: Upload process failed with error:', error);
    console.error('📋 Complete error details:', {
      name: error.name,
      message: error.message,
      code: error.code,
      statusCode: error.$metadata?.httpStatusCode,
      requestId: error.$metadata?.requestId,
      stack: error.stack?.split('\n').slice(0, 5).join('\n') // First 5 lines of stack trace
    });

    // If this is already a formatted error from the upload section, re-throw it
    if (error.message.startsWith('AWS_') || error.message.startsWith('VALIDATION_ERROR:')) {
      throw error;
    }

    // Handle other types of errors
    if (error.name === 'CredentialsProviderError') {
      throw new Error('AWS_CREDENTIALS_ERROR: AWS credentials not configured properly. Please check your environment variables.');
    } else if (error.name === 'AccessDenied' || error.code === 'AccessDenied') {
      throw new Error('AWS_ACCESS_DENIED: Access denied to S3 bucket. Please check bucket permissions.');
    } else if (error.name === 'NoSuchBucket' || error.code === 'NoSuchBucket') {
      throw new Error(`AWS_BUCKET_NOT_FOUND: S3 bucket '${BUCKET_NAME}' does not exist.`);
    } else if (error.message?.includes('network') || error.message?.includes('fetch')) {
      throw new Error('AWS_NETWORK_ERROR: Network error during upload. Please check your internet connection.');
    } else {
      throw new Error(`AWS_GENERAL_ERROR: Upload failed - ${error.message}`);
    }
  }
};

const updateMetadataManifest = async (metadata) => {
  try {
    // If AWS is not configured, simulate metadata update
    if (!s3Client) {
      console.log('AWS not configured. Simulating metadata manifest update...');
      console.log('Metadata:', metadata);
      return;
    }

    // Try to get existing manifest
    const manifestKey = 'metadata.csv';
    let existingContent = '';

    try {
      const headResult = await s3Client.send(
        new HeadObjectCommand({
          Bucket: BUCKET_NAME,
          Key: manifestKey
        })
      );

      if (headResult) {
        const getResult = await s3Client.send(
          new GetObjectCommand({
            Bucket: BUCKET_NAME,
            Key: manifestKey
          })
        );
        existingContent = await getResult.Body.transformToString();
      }
    } catch (err) {
      // Manifest doesn't exist yet, we'll create it
      console.log('Creating new metadata manifest');
    }

    // Add header if this is a new file
    if (!existingContent) {
      existingContent = 'filename,phrase_label,user_id,agegroup,gender,ethnicity,timestamp\n';
    }

    // Add new row
    const newRow = [
      metadata.filename,
      metadata.phrase,
      metadata.userId || 'user01',
      metadata.ageGroup || '40to64',
      metadata.gender || 'female',
      metadata.ethnicity || 'not_specified',
      metadata.timestamp
    ].join(',') + '\n';

    // Upload updated manifest
    await s3Client.send(
      new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: manifestKey,
        Body: existingContent + newRow,
        ContentType: 'text/csv'
      })
    );
  } catch (error) {
    console.error('Error updating metadata manifest:', error);
    // Don't throw here - we don't want to fail the video upload if manifest update fails
  }
};

// For backward compatibility
export const uploadVideo = uploadVideoToS3;
