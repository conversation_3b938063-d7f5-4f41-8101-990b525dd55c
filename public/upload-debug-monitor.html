<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Debug Monitor - ICU Dataset Application</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background-color: #1a1a1a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
            font-size: 12px;
        }
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .monitor-section {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            margin: 10px 0;
            padding: 15px;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #00ff00;
            background: rgba(0, 255, 0, 0.1);
        }
        .error { border-left-color: #ff4444; background: rgba(255, 68, 68, 0.1); color: #ff8888; }
        .warning { border-left-color: #ffaa00; background: rgba(255, 170, 0, 0.1); color: #ffcc66; }
        .info { border-left-color: #4488ff; background: rgba(68, 136, 255, 0.1); color: #88bbff; }
        .success { border-left-color: #44ff44; background: rgba(68, 255, 68, 0.1); color: #88ff88; }
        .timestamp { color: #888; font-size: 10px; }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-family: inherit;
        }
        .test-button:hover { background: #45a049; }
        .test-button.danger { background: #f44336; }
        .test-button.danger:hover { background: #da190b; }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            background: #333;
            border: 1px solid #555;
            border-radius: 8px;
            padding: 15px;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .online { background: #4CAF50; }
        .offline { background: #f44336; }
        .unknown { background: #ff9800; }
        #logContainer {
            max-height: 400px;
            overflow-y: auto;
            background: #1a1a1a;
            border: 1px solid #444;
            padding: 10px;
            border-radius: 5px;
        }
        .clear-logs {
            float: right;
            background: #666;
            font-size: 10px;
            padding: 5px 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 UPLOAD DEBUG MONITOR</h1>
        <p>Real-time monitoring of ICU Dataset Application upload workflow</p>
    </div>

    <div class="status-grid">
        <div class="status-card">
            <h3><span id="reactStatus" class="status-indicator unknown"></span>React Server (3003)</h3>
            <div id="reactInfo">Checking...</div>
        </div>
        <div class="status-card">
            <h3><span id="backendStatus" class="status-indicator unknown"></span>Backend Server (5000)</h3>
            <div id="backendInfo">Checking...</div>
        </div>
        <div class="status-card">
            <h3><span id="s3Status" class="status-indicator unknown"></span>S3 Connectivity</h3>
            <div id="s3Info">Checking...</div>
        </div>
        <div class="status-card">
            <h3><span id="uploadStatus" class="status-indicator unknown"></span>Upload Endpoint</h3>
            <div id="uploadInfo">Checking...</div>
        </div>
    </div>

    <div class="monitor-section">
        <h2>🎯 Upload Workflow Tests</h2>
        <button class="test-button" onclick="testFullWorkflow()">Test Complete Workflow</button>
        <button class="test-button" onclick="testDirectS3Upload()">Test Direct S3 Upload</button>
        <button class="test-button" onclick="testBackendUpload()">Test Backend Upload</button>
        <button class="test-button" onclick="monitorNetworkRequests()">Monitor Network</button>
        <button class="test-button danger" onclick="clearLogs()">Clear Logs</button>
    </div>

    <div class="monitor-section">
        <h2>📊 Real-time Logs <button class="clear-logs" onclick="clearLogs()">Clear</button></h2>
        <div id="logContainer"></div>
    </div>

    <div class="monitor-section">
        <h2>🔗 Quick Links</h2>
        <p><strong>Main App:</strong> <a href="http://localhost:3003" target="_blank" style="color: #4CAF50;">http://localhost:3003</a></p>
        <p><strong>Backend Health:</strong> <a href="http://localhost:5000/health" target="_blank" style="color: #4CAF50;">http://localhost:5000/health</a></p>
        <p><strong>S3 Test:</strong> <a href="http://localhost:5000/api/test-s3" target="_blank" style="color: #4CAF50;">http://localhost:5000/api/test-s3</a></p>
    </div>

    <script>
        let logContainer = document.getElementById('logContainer');
        let networkMonitoring = false;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function clearLogs() {
            logContainer.innerHTML = '';
        }

        function updateStatus(elementId, status, info) {
            const indicator = document.getElementById(elementId);
            const infoDiv = document.getElementById(elementId.replace('Status', 'Info'));
            
            indicator.className = `status-indicator ${status}`;
            infoDiv.innerHTML = info;
        }

        async function checkReactServer() {
            try {
                const response = await fetch('http://localhost:3003');
                if (response.ok) {
                    updateStatus('reactStatus', 'online', 'ONLINE - Ready for testing');
                    log('✅ React server responding normally', 'success');
                    return true;
                } else {
                    updateStatus('reactStatus', 'offline', `ERROR - Status ${response.status}`);
                    log(`❌ React server error: ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                updateStatus('reactStatus', 'offline', `OFFLINE - ${error.message}`);
                log(`❌ React server offline: ${error.message}`, 'error');
                return false;
            }
        }

        async function checkBackendServer() {
            try {
                const response = await fetch('http://localhost:5000/health');
                if (response.ok) {
                    const data = await response.json();
                    updateStatus('backendStatus', 'online', `ONLINE - ${data.status} (${Math.round(data.uptime)}s uptime)`);
                    log(`✅ Backend server healthy: ${data.status}`, 'success');
                    return true;
                } else {
                    updateStatus('backendStatus', 'offline', `ERROR - Status ${response.status}`);
                    log(`❌ Backend server error: ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                updateStatus('backendStatus', 'offline', `OFFLINE - ${error.message}`);
                log(`❌ Backend server offline: ${error.message}`, 'error');
                return false;
            }
        }

        async function checkS3Connectivity() {
            try {
                const response = await fetch('http://localhost:5000/api/test-s3');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        updateStatus('s3Status', 'online', `CONNECTED - ${data.bucket} (${data.objectCount} objects)`);
                        log(`✅ S3 connectivity verified: ${data.objectCount} objects in ${data.bucket}`, 'success');
                        return true;
                    } else {
                        updateStatus('s3Status', 'offline', `FAILED - ${data.error || 'Unknown error'}`);
                        log(`❌ S3 connectivity failed: ${data.error}`, 'error');
                        return false;
                    }
                } else {
                    updateStatus('s3Status', 'offline', `ERROR - Status ${response.status}`);
                    log(`❌ S3 test endpoint error: ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                updateStatus('s3Status', 'offline', `FAILED - ${error.message}`);
                log(`❌ S3 connectivity test failed: ${error.message}`, 'error');
                return false;
            }
        }

        async function checkUploadEndpoint() {
            try {
                const formData = new FormData();
                formData.append('test', 'connectivity-check');
                
                const response = await fetch('http://localhost:5000/upload', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    updateStatus('uploadStatus', 'online', 'ACCESSIBLE - Ready for uploads');
                    log('✅ Upload endpoint accessible', 'success');
                    return true;
                } else {
                    const errorText = await response.text();
                    updateStatus('uploadStatus', 'offline', `ERROR - Status ${response.status}`);
                    log(`❌ Upload endpoint error: ${response.status} - ${errorText.substring(0, 100)}`, 'error');
                    return false;
                }
            } catch (error) {
                updateStatus('uploadStatus', 'offline', `FAILED - ${error.message}`);
                log(`❌ Upload endpoint test failed: ${error.message}`, 'error');
                return false;
            }
        }

        async function testDirectS3Upload() {
            log('🔄 Testing direct S3 upload simulation...', 'info');
            
            // This would require AWS SDK setup in browser - for now just log the attempt
            log('ℹ️ Direct S3 upload requires AWS SDK initialization in browser context', 'warning');
            log('💡 Use main application to test actual S3 uploads with proper credentials', 'info');
        }

        async function testBackendUpload() {
            log('🔄 Testing backend upload with sample data...', 'info');
            
            try {
                const formData = new FormData();
                
                // Create a small test "video" file
                const testVideoData = new Blob(['FAKE_VIDEO_DATA_FOR_TESTING'], { type: 'video/mp4' });
                formData.append('video', testVideoData, 'test-upload.mp4');
                formData.append('ageGroup', '18to39');
                formData.append('gender', 'male');
                formData.append('ethnicity', 'test');
                formData.append('phrase', 'test-phrase');
                
                log('📤 Sending test upload to backend...', 'info');
                
                const response = await fetch('http://localhost:5000/upload', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ Backend upload successful: ${JSON.stringify(result)}`, 'success');
                } else {
                    const errorText = await response.text();
                    log(`❌ Backend upload failed: ${response.status} - ${errorText}`, 'error');
                }
            } catch (error) {
                log(`❌ Backend upload test error: ${error.message}`, 'error');
            }
        }

        async function testFullWorkflow() {
            log('🎯 Starting complete workflow test...', 'info');
            
            const reactOk = await checkReactServer();
            const backendOk = await checkBackendServer();
            const s3Ok = await checkS3Connectivity();
            const uploadOk = await checkUploadEndpoint();
            
            if (reactOk && backendOk && s3Ok && uploadOk) {
                log('🎉 All systems operational - ready for video recording test!', 'success');
                log('💡 Open main app and record videos to test complete workflow', 'info');
            } else {
                log('⚠️ Some systems not operational - check individual components', 'warning');
            }
        }

        function monitorNetworkRequests() {
            if (networkMonitoring) {
                log('⏹️ Stopping network monitoring', 'info');
                networkMonitoring = false;
                return;
            }
            
            networkMonitoring = true;
            log('🔍 Starting network request monitoring...', 'info');
            log('💡 Open browser DevTools Network tab for detailed request monitoring', 'info');
            
            // This would require more complex setup to actually monitor network requests
            // For now, just provide guidance
            setTimeout(() => {
                if (networkMonitoring) {
                    log('📊 Network monitoring active - check DevTools for request details', 'info');
                }
            }, 2000);
        }

        // Auto-run initial checks
        window.onload = function() {
            log('🚀 Upload Debug Monitor initialized', 'success');
            setTimeout(testFullWorkflow, 1000);
        };

        // Auto-refresh status every 30 seconds
        setInterval(() => {
            if (!networkMonitoring) {
                testFullWorkflow();
            }
        }, 30000);
    </script>
</body>
</html>
