<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ICU Dataset Application - Upload Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { color: #4caf50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196f3; }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1976d2; }
        pre {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #4caf50; }
        .status-error { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
    </style>
</head>
<body>
    <h1>🔍 ICU Dataset Application - Upload Debug Test</h1>
    <p>This page tests the upload functionality in the browser environment to identify upload errors.</p>

    <div class="test-section">
        <h2>📋 Environment Variables Check</h2>
        <button onclick="checkEnvironment()">Check Environment</button>
        <div id="env-results"></div>
    </div>

    <div class="test-section">
        <h2>🔧 AWS S3 Client Initialization</h2>
        <button onclick="testS3Client()">Test S3 Client</button>
        <div id="s3-results"></div>
    </div>

    <div class="test-section">
        <h2>🌐 Backend Connectivity</h2>
        <button onclick="testBackend()">Test Backend</button>
        <div id="backend-results"></div>
    </div>

    <div class="test-section">
        <h2>🎥 Mock Upload Test</h2>
        <button onclick="testMockUpload()">Test Mock Upload</button>
        <div id="upload-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 Console Logs</h2>
        <button onclick="clearConsole()">Clear Console</button>
        <pre id="console-output"></pre>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        let consoleOutput = [];

        function captureConsole(type, args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.push(`[${type.toUpperCase()}] ${new Date().toLocaleTimeString()}: ${message}`);
            updateConsoleDisplay();
        }

        console.log = (...args) => {
            originalLog.apply(console, args);
            captureConsole('log', args);
        };

        console.error = (...args) => {
            originalError.apply(console, args);
            captureConsole('error', args);
        };

        console.warn = (...args) => {
            originalWarn.apply(console, args);
            captureConsole('warn', args);
        };

        function updateConsoleDisplay() {
            document.getElementById('console-output').textContent = consoleOutput.slice(-50).join('\n');
        }

        function clearConsole() {
            consoleOutput = [];
            updateConsoleDisplay();
        }

        function addResult(elementId, html) {
            document.getElementById(elementId).innerHTML += html + '<br>';
        }

        function checkEnvironment() {
            const envResults = document.getElementById('env-results');
            envResults.innerHTML = '<h3>Environment Variables:</h3>';

            // Check if we're in React app context
            const isReactApp = window.location.hostname === 'localhost' && window.location.port === '3003';
            
            if (isReactApp) {
                addResult('env-results', '<span class="success">✅ Running in React development environment</span>');
                
                // In React, environment variables are available via process.env
                const envVars = {
                    'REACT_APP_AWS_IDENTITY_POOL_ID': 'Should be set',
                    'REACT_APP_AWS_REGION': 'Should be set',
                    'REACT_APP_S3_BUCKET': 'Should be set',
                    'REACT_APP_BACKEND_URL': 'Should be set'
                };

                Object.keys(envVars).forEach(key => {
                    // Note: In browser, we can't directly access process.env
                    // The values are injected at build time
                    addResult('env-results', `<span class="info">ℹ️ ${key}: Available at build time</span>`);
                });
            } else {
                addResult('env-results', '<span class="warning">⚠️ Not running in React development environment</span>');
                addResult('env-results', '<span class="info">ℹ️ Environment variables are injected at build time in React apps</span>');
            }
        }

        async function testS3Client() {
            const s3Results = document.getElementById('s3-results');
            s3Results.innerHTML = '<h3>S3 Client Test:</h3>';

            try {
                // Try to access the AWS storage service
                if (window.awsStorage) {
                    addResult('s3-results', '<span class="success">✅ AWS Storage service available</span>');
                    
                    // Test AWS connection
                    const testResult = await window.awsStorage.testAWSConnection();
                    if (testResult.success) {
                        addResult('s3-results', '<span class="success">✅ AWS connection test passed</span>');
                        addResult('s3-results', `<span class="info">📊 Objects found: ${testResult.objectCount || 0}</span>`);
                    } else {
                        addResult('s3-results', '<span class="error">❌ AWS connection test failed</span>');
                        addResult('s3-results', `<span class="error">Error: ${testResult.error}</span>`);
                    }
                } else {
                    addResult('s3-results', '<span class="warning">⚠️ AWS Storage service not available in this context</span>');
                    addResult('s3-results', '<span class="info">ℹ️ This test needs to be run within the React application</span>');
                }
            } catch (error) {
                addResult('s3-results', '<span class="error">❌ S3 Client test failed</span>');
                addResult('s3-results', `<span class="error">Error: ${error.message}</span>`);
            }
        }

        async function testBackend() {
            const backendResults = document.getElementById('backend-results');
            backendResults.innerHTML = '<h3>Backend Connectivity Test:</h3>';

            const backendUrl = 'http://localhost:5000';
            
            try {
                addResult('backend-results', `<span class="info">🔍 Testing backend at: ${backendUrl}</span>`);
                
                // Test health endpoint
                const healthResponse = await fetch(`${backendUrl}/health`);
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    addResult('backend-results', '<span class="success">✅ Backend health check passed</span>');
                    addResult('backend-results', `<span class="info">Status: ${healthData.status}</span>`);
                    addResult('backend-results', `<span class="info">Uptime: ${healthData.uptime}s</span>`);
                    addResult('backend-results', `<span class="info">AWS Status: ${healthData.services?.aws || 'unknown'}</span>`);
                } else {
                    addResult('backend-results', `<span class="error">❌ Backend health check failed: HTTP ${healthResponse.status}</span>`);
                }

                // Test sample counts endpoint
                const countsResponse = await fetch(`${backendUrl}/api/sample-counts`);
                if (countsResponse.ok) {
                    const countsData = await countsResponse.json();
                    if (countsData.success) {
                        addResult('backend-results', '<span class="success">✅ Sample counts API working</span>');
                        addResult('backend-results', `<span class="info">Total recordings: ${countsData.counts.total}</span>`);
                    } else {
                        addResult('backend-results', '<span class="error">❌ Sample counts API error</span>');
                    }
                } else {
                    addResult('backend-results', `<span class="error">❌ Sample counts API failed: HTTP ${countsResponse.status}</span>`);
                }

            } catch (error) {
                addResult('backend-results', '<span class="error">❌ Backend connectivity test failed</span>');
                addResult('backend-results', `<span class="error">Error: ${error.message}</span>`);
                
                if (error.message.includes('fetch')) {
                    addResult('backend-results', '<span class="warning">💡 This might be a CORS issue or the backend server is not running</span>');
                }
            }
        }

        async function testMockUpload() {
            const uploadResults = document.getElementById('upload-results');
            uploadResults.innerHTML = '<h3>Mock Upload Test:</h3>';

            try {
                // Create a more realistic mock video blob
                const canvas = document.createElement('canvas');
                canvas.width = 150;
                canvas.height = 75;
                const ctx = canvas.getContext('2d');
                ctx.fillStyle = '#000000';
                ctx.fillRect(0, 0, 150, 75);
                ctx.fillStyle = '#ffffff';
                ctx.fillText('Mock Video Frame', 10, 40);

                // Convert canvas to blob
                const mockBlob = await new Promise(resolve => {
                    canvas.toBlob(resolve, 'video/webm');
                });

                addResult('upload-results', `<span class="info">📊 Mock video blob created: ${mockBlob.size} bytes, type: ${mockBlob.type}</span>`);

                const testConfig = {
                    phrase: 'test-phrase',
                    demographics: {
                        userId: 'test-user',
                        ageGroup: '40to64',
                        gender: 'female',
                        ethnicity: 'caucasian',
                        category: 'test'
                    },
                    recordingNumber: 1
                };

                // Test direct backend upload first
                addResult('upload-results', '<span class="info">🔄 Testing direct backend upload...</span>');

                try {
                    const formData = new FormData();
                    formData.append('video', mockBlob, 'test-video.webm');
                    formData.append('phrase', testConfig.phrase);
                    formData.append('demographics', JSON.stringify(testConfig.demographics));
                    formData.append('recordingNumber', testConfig.recordingNumber.toString());

                    const backendResponse = await fetch('http://localhost:5000/upload', {
                        method: 'POST',
                        body: formData
                    });

                    if (backendResponse.ok) {
                        const backendResult = await backendResponse.json();
                        addResult('upload-results', '<span class="success">✅ Direct backend upload successful</span>');
                        addResult('upload-results', `<span class="info">Backend result: ${JSON.stringify(backendResult, null, 2)}</span>`);
                    } else {
                        const errorText = await backendResponse.text();
                        addResult('upload-results', `<span class="error">❌ Direct backend upload failed: HTTP ${backendResponse.status}</span>`);
                        addResult('upload-results', `<span class="error">Error response: ${errorText}</span>`);
                    }
                } catch (backendError) {
                    addResult('upload-results', '<span class="error">❌ Direct backend upload failed</span>');
                    addResult('upload-results', `<span class="error">Backend error: ${backendError.message}</span>`);

                    // Analyze backend error
                    if (backendError.message.includes('fetch')) {
                        addResult('upload-results', '<span class="warning">💡 Network connectivity issue to backend</span>');
                    } else if (backendError.message.includes('CORS')) {
                        addResult('upload-results', '<span class="warning">💡 CORS issue with backend</span>');
                    }
                }

                // Test through React app upload service if available
                if (window.awsStorage && window.awsStorage.uploadVideoToS3) {
                    addResult('upload-results', '<span class="info">🚀 Testing through React app upload service...</span>');

                    const result = await window.awsStorage.uploadVideoToS3(
                        mockBlob,
                        testConfig.phrase,
                        testConfig.demographics,
                        testConfig.recordingNumber
                    );

                    addResult('upload-results', '<span class="success">✅ React app upload successful</span>');
                    addResult('upload-results', `<span class="info">Result: ${JSON.stringify(result, null, 2)}</span>`);

                } else {
                    addResult('upload-results', '<span class="warning">⚠️ React app upload service not available</span>');
                    addResult('upload-results', '<span class="info">ℹ️ This test needs to be run within the React application context</span>');
                }

            } catch (error) {
                addResult('upload-results', '<span class="error">❌ Mock upload test failed</span>');
                addResult('upload-results', `<span class="error">Error: ${error.name}: ${error.message}</span>`);
                addResult('upload-results', `<span class="error">Stack: ${error.stack?.split('\n').slice(0, 3).join('\n')}</span>`);

                // Analyze error type
                if (error.message.includes('CORS')) {
                    addResult('upload-results', '<span class="warning">💡 CORS issue detected - check S3 bucket CORS policy</span>');
                } else if (error.message.includes('credentials')) {
                    addResult('upload-results', '<span class="warning">💡 AWS credentials issue</span>');
                } else if (error.message.includes('fetch')) {
                    addResult('upload-results', '<span class="warning">💡 Network connectivity issue</span>');
                } else if (error.message.includes('backend')) {
                    addResult('upload-results', '<span class="warning">💡 Backend server issue</span>');
                } else if (error.message.includes('toBlob')) {
                    addResult('upload-results', '<span class="warning">💡 Canvas/Blob creation issue</span>');
                }
            }
        }

        // Auto-run environment check on page load
        window.addEventListener('load', () => {
            checkEnvironment();
        });
    </script>
</body>
</html>
