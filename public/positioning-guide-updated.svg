<svg width="800" height="600" viewBox="0 0 800 600" xmlns="http://www.w3.org/2000/svg">
  <!-- Person silhouette -->
  <g fill="black">
    <!-- Head -->
    <ellipse cx="150" cy="200" rx="80" ry="100"/>
    <!-- Neck -->
    <rect x="130" y="280" width="40" height="40"/>
    <!-- Body -->
    <rect x="80" y="320" width="140" height="200"/>
    <!-- Face profile -->
    <path d="M 150 150 Q 200 160 220 200 Q 210 240 200 280 Q 180 300 150 300 Z"/>
  </g>
  
  <!-- Laptop -->
  <g fill="black">
    <!-- Screen -->
    <rect x="500" y="250" width="200" height="120" rx="10"/>
    <!-- Base -->
    <ellipse cx="600" cy="380" rx="120" ry="15"/>
    <!-- Keyboard area -->
    <rect x="480" y="365" width="240" height="20" rx="5"/>
  </g>
  
  <!-- Distance arrow and text -->
  <g>
    <!-- Arrow line -->
    <line x1="270" y1="300" x2="480" y2="300" stroke="black" stroke-width="4"/>
    <!-- Left arrow head -->
    <polygon points="270,300 290,290 290,310" fill="black"/>
    <!-- Right arrow head -->
    <polygon points="480,300 460,290 460,310" fill="black"/>
    
    <!-- Distance text -->
    <text x="375" y="280" text-anchor="middle" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="black">30 cm</text>
  </g>
</svg>
