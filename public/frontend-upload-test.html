<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Upload Test - ICU Dataset Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { background-color: #e8f5e8; border-left: 4px solid #4CAF50; padding: 10px; margin: 10px 0; }
        .error { background-color: #ffeaea; border-left: 4px solid #f44336; padding: 10px; margin: 10px 0; }
        .info { background-color: #e3f2fd; border-left: 4px solid #2196F3; padding: 10px; margin: 10px 0; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 10px; margin: 10px 0; }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        .test-button:hover { background-color: #45a049; }
        .test-button:disabled { background-color: #cccccc; cursor: not-allowed; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }
        .status-card {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 Frontend Upload Test</h1>
        <p>Testing ICU Dataset Application frontend upload functionality</p>
    </div>

    <div class="test-section">
        <h2>🔧 Environment Configuration Test</h2>
        <button class="test-button" onclick="testEnvironmentConfig()">Check Environment Variables</button>
        <div id="env-results"></div>
    </div>

    <div class="test-section">
        <h2>🎥 Simulated Frontend Upload Test</h2>
        <button class="test-button" onclick="testFrontendUpload()">Test Frontend Upload Flow</button>
        <div id="frontend-results"></div>
    </div>

    <div class="test-section">
        <h2>📊 Upload Mode Detection</h2>
        <button class="test-button" onclick="testUploadModeDetection()">Check Upload Mode</button>
        <div id="mode-results"></div>
    </div>

    <div class="status-grid">
        <div class="status-card">
            <h3>🔗 Quick Links</h3>
            <p><a href="http://localhost:3003" target="_blank">Main Application</a></p>
            <p><a href="http://localhost:3003/upload-debug-monitor.html" target="_blank">Debug Monitor</a></p>
            <p><a href="http://localhost:5000/health" target="_blank">Backend Health</a></p>
        </div>
        <div class="status-card">
            <h3>📋 Test Results Summary</h3>
            <div id="summary-results">Run tests to see results...</div>
        </div>
    </div>

    <script>
        let testResults = {
            environment: null,
            frontend: null,
            mode: null
        };

        function updateSummary() {
            const summary = document.getElementById('summary-results');
            const passed = Object.values(testResults).filter(r => r === true).length;
            const total = Object.values(testResults).filter(r => r !== null).length;
            
            if (total === 0) {
                summary.innerHTML = 'Run tests to see results...';
                return;
            }
            
            summary.innerHTML = `
                <div class="${passed === total ? 'success' : 'warning'}">
                    Tests Passed: ${passed}/${total}<br>
                    ${passed === total ? '✅ All systems operational!' : '⚠️ Some issues detected'}
                </div>
            `;
        }

        function testEnvironmentConfig() {
            const resultsDiv = document.getElementById('env-results');
            resultsDiv.innerHTML = '<div class="info">🔄 Checking environment configuration...</div>';
            
            // Since we're in the browser, we can't directly access process.env
            // But we can test if the React app is configured correctly by checking for backend connectivity
            
            fetch('http://localhost:5000/health')
                .then(response => response.json())
                .then(data => {
                    resultsDiv.innerHTML = `
                        <div class="success">✅ Backend server accessible</div>
                        <div class="info">📊 Backend status: ${data.status}</div>
                        <div class="info">⏱️ Uptime: ${Math.round(data.uptime)}s</div>
                        <div class="info">🔧 Environment: ${data.environment}</div>
                        <div class="info">🌐 Services: ${JSON.stringify(data.services)}</div>
                    `;
                    testResults.environment = true;
                    updateSummary();
                })
                .catch(error => {
                    resultsDiv.innerHTML = `
                        <div class="error">❌ Backend server not accessible: ${error.message}</div>
                        <div class="warning">⚠️ Make sure backend server is running on port 5000</div>
                    `;
                    testResults.environment = false;
                    updateSummary();
                });
        }

        function testFrontendUpload() {
            const resultsDiv = document.getElementById('frontend-results');
            resultsDiv.innerHTML = '<div class="info">🔄 Testing frontend upload simulation...</div>';
            
            // Create a mock video blob similar to what the real app would create
            const canvas = document.createElement('canvas');
            canvas.width = 150;
            canvas.height = 75;
            const ctx = canvas.getContext('2d');
            
            // Draw a simple test pattern
            ctx.fillStyle = '#000000';
            ctx.fillRect(0, 0, 150, 75);
            ctx.fillStyle = '#ffffff';
            ctx.font = '12px Arial';
            ctx.fillText('Test Video', 10, 40);
            
            canvas.toBlob(async (blob) => {
                try {
                    // Simulate the exact FormData structure used by the real app
                    const formData = new FormData();
                    formData.append('video', blob, 'test_frontend_upload.webm');
                    formData.append('phrase', 'I need water');
                    formData.append('category', 'Basic Needs');
                    formData.append('recordingNumber', '1');
                    
                    const demographics = {
                        userId: 'frontend-test-' + Date.now(),
                        ageGroup: '40to64',
                        gender: 'female',
                        ethnicity: 'caucasian',
                        category: 'Basic Needs'
                    };
                    
                    formData.append('demographics', JSON.stringify(demographics));
                    
                    resultsDiv.innerHTML += '<div class="info">📤 Sending upload request...</div>';
                    
                    const response = await fetch('http://localhost:5000/upload', {
                        method: 'POST',
                        body: formData
                    });
                    
                    if (response.ok) {
                        const result = await response.json();
                        resultsDiv.innerHTML += `
                            <div class="success">✅ Frontend upload successful!</div>
                            <div class="info">📁 File path: ${result.filePath}</div>
                            <div class="info">🔗 S3 URL: ${result.url}</div>
                            <div class="info">📏 Blob size: ${blob.size} bytes</div>
                        `;
                        testResults.frontend = true;
                    } else {
                        const errorText = await response.text();
                        resultsDiv.innerHTML += `
                            <div class="error">❌ Frontend upload failed: ${response.status}</div>
                            <div class="error">📄 Error: ${errorText}</div>
                        `;
                        testResults.frontend = false;
                    }
                } catch (error) {
                    resultsDiv.innerHTML += `
                        <div class="error">❌ Frontend upload error: ${error.message}</div>
                    `;
                    testResults.frontend = false;
                }
                updateSummary();
            }, 'video/webm');
        }

        function testUploadModeDetection() {
            const resultsDiv = document.getElementById('mode-results');
            resultsDiv.innerHTML = '<div class="info">🔄 Testing upload mode detection...</div>';
            
            // Test both direct S3 and backend upload modes
            Promise.all([
                fetch('http://localhost:5000/api/test-s3'),
                fetch('http://localhost:5000/health')
            ]).then(async ([s3Response, healthResponse]) => {
                const s3Data = await s3Response.json();
                const healthData = await healthResponse.json();
                
                resultsDiv.innerHTML = `
                    <div class="success">✅ Upload mode detection successful</div>
                    <div class="info">🔧 Backend mode: Available (recommended)</div>
                    <div class="info">🌐 S3 connectivity: ${s3Data.success ? 'Connected' : 'Failed'}</div>
                    <div class="info">📊 S3 bucket: ${s3Data.bucket} (${s3Data.objectCount} objects)</div>
                    <div class="info">⚙️ AWS services: ${healthData.services.aws}</div>
                    <div class="warning">💡 App will use backend upload mode (preferred for reliability)</div>
                `;
                testResults.mode = true;
                updateSummary();
            }).catch(error => {
                resultsDiv.innerHTML = `
                    <div class="error">❌ Upload mode detection failed: ${error.message}</div>
                `;
                testResults.mode = false;
                updateSummary();
            });
        }

        // Auto-run environment test on page load
        window.onload = function() {
            setTimeout(testEnvironmentConfig, 1000);
        };
    </script>
</body>
</html>
