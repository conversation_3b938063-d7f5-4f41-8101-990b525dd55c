<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Test Verification - ICU Dataset Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #4CAF50; }
        .status-error { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
        .status-info { background-color: #2196F3; }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        .success { background-color: #e8f5e8; border-left: 4px solid #4CAF50; }
        .error { background-color: #ffeaea; border-left: 4px solid #f44336; }
        .info { background-color: #e3f2fd; border-left: 4px solid #2196F3; }
        .test-button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #45a049;
        }
        .test-button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .url-link {
            color: #1976d2;
            text-decoration: none;
            font-weight: bold;
        }
        .url-link:hover {
            text-decoration: underline;
        }
        .critical-note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 Upload Test Verification</h1>
        <p>ICU Dataset Application - Complete Upload Workflow Testing</p>
    </div>

    <div class="critical-note">
        <h3>🚨 CRITICAL FIXES IMPLEMENTED:</h3>
        <ul>
            <li><strong>Video Cropping Fixed:</strong> Lips now centered at 50% (was 38%) with privacy boundary at 45% (was 57%)</li>
            <li><strong>S3 Authentication Fixed:</strong> All backend S3Client instances now have proper AWS credentials</li>
            <li><strong>Server Status:</strong> Both React (3003) and Backend (5000) servers confirmed running</li>
        </ul>
    </div>

    <div class="test-section">
        <h2><span class="status-indicator status-info"></span>Server Status Tests</h2>
        <div id="server-tests">
            <button class="test-button" onclick="testReactServer()">Test React Server (3003)</button>
            <button class="test-button" onclick="testBackendServer()">Test Backend Server (5000)</button>
            <button class="test-button" onclick="testS3Connectivity()">Test S3 Connectivity</button>
        </div>
        <div id="server-results"></div>
    </div>

    <div class="test-section">
        <h2><span class="status-indicator status-info"></span>Upload Workflow Tests</h2>
        <div id="upload-tests">
            <button class="test-button" onclick="testUploadEndpoint()">Test Upload Endpoint</button>
            <button class="test-button" onclick="simulateVideoUpload()">Simulate Video Upload</button>
            <button class="test-button" onclick="checkBucketContents()">Check S3 Bucket Contents</button>
        </div>
        <div id="upload-results"></div>
    </div>

    <div class="test-section">
        <h2><span class="status-indicator status-success"></span>Direct Application Links</h2>
        <p><strong>Main Application:</strong> <a href="http://localhost:3003" class="url-link" target="_blank">http://localhost:3003</a></p>
        <p><strong>Upload Diagnostic:</strong> <a href="http://localhost:3003/upload-mode-diagnostic.html" class="url-link" target="_blank">http://localhost:3003/upload-mode-diagnostic.html</a></p>
        <p><strong>Backend Health:</strong> <a href="http://localhost:5000/health" class="url-link" target="_blank">http://localhost:5000/health</a></p>
        <p><strong>S3 Test:</strong> <a href="http://localhost:5000/api/test-s3" class="url-link" target="_blank">http://localhost:5000/api/test-s3</a></p>
    </div>

    <div class="test-section">
        <h2><span class="status-indicator status-warning"></span>Manual Testing Instructions</h2>
        <ol>
            <li><strong>Open Main App:</strong> <a href="http://localhost:3003" class="url-link" target="_blank">http://localhost:3003</a></li>
            <li><strong>Complete Flow:</strong> Consent → Demographics → Training → Phrase Selection</li>
            <li><strong>Record Videos:</strong> Select a phrase and record 3 videos</li>
            <li><strong>Watch Console:</strong> Look for upload success messages</li>
            <li><strong>Verify Auto-advance:</strong> Should trigger after 3 recordings</li>
        </ol>
        
        <h3>Expected Console Messages:</h3>
        <div class="test-result success">
            ✅ AWS S3 client initialized successfully<br>
            🔧 Backend request details: {...}<br>
            ✅ Backend upload successful<br>
            📤 Upload completed successfully<br>
            🎉 Auto-advance triggered after 3 recordings
        </div>
    </div>

    <script>
        async function testReactServer() {
            const resultDiv = document.getElementById('server-results');
            try {
                const response = await fetch('http://localhost:3003');
                if (response.ok) {
                    resultDiv.innerHTML += '<div class="test-result success">✅ React Server (3003): ONLINE</div>';
                } else {
                    resultDiv.innerHTML += '<div class="test-result error">❌ React Server (3003): ERROR - Status ' + response.status + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML += '<div class="test-result error">❌ React Server (3003): OFFLINE - ' + error.message + '</div>';
            }
        }

        async function testBackendServer() {
            const resultDiv = document.getElementById('server-results');
            try {
                const response = await fetch('http://localhost:5000/health');
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML += '<div class="test-result success">✅ Backend Server (5000): ONLINE - ' + data.status + '</div>';
                } else {
                    resultDiv.innerHTML += '<div class="test-result error">❌ Backend Server (5000): ERROR - Status ' + response.status + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML += '<div class="test-result error">❌ Backend Server (5000): OFFLINE - ' + error.message + '</div>';
            }
        }

        async function testS3Connectivity() {
            const resultDiv = document.getElementById('server-results');
            try {
                const response = await fetch('http://localhost:5000/api/test-s3');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success) {
                        resultDiv.innerHTML += '<div class="test-result success">✅ S3 Connectivity: SUCCESS - Bucket: ' + data.bucket + ' (' + data.objectCount + ' objects)</div>';
                    } else {
                        resultDiv.innerHTML += '<div class="test-result error">❌ S3 Connectivity: FAILED - ' + (data.error || 'Unknown error') + '</div>';
                    }
                } else {
                    resultDiv.innerHTML += '<div class="test-result error">❌ S3 Connectivity: ERROR - Status ' + response.status + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML += '<div class="test-result error">❌ S3 Connectivity: FAILED - ' + error.message + '</div>';
            }
        }

        async function testUploadEndpoint() {
            const resultDiv = document.getElementById('upload-results');
            try {
                const formData = new FormData();
                formData.append('test', 'upload-test');
                
                const response = await fetch('http://localhost:5000/upload', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    resultDiv.innerHTML += '<div class="test-result success">✅ Upload Endpoint: ACCESSIBLE</div>';
                } else {
                    resultDiv.innerHTML += '<div class="test-result error">❌ Upload Endpoint: ERROR - Status ' + response.status + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML += '<div class="test-result error">❌ Upload Endpoint: FAILED - ' + error.message + '</div>';
            }
        }

        async function simulateVideoUpload() {
            const resultDiv = document.getElementById('upload-results');
            resultDiv.innerHTML += '<div class="test-result info">🔄 Simulating video upload...</div>';
            
            // This would require actual video data - for now just test the endpoint
            resultDiv.innerHTML += '<div class="test-result info">ℹ️ Video upload simulation requires actual video data. Use manual testing instead.</div>';
        }

        async function checkBucketContents() {
            const resultDiv = document.getElementById('upload-results');
            try {
                const response = await fetch('http://localhost:5000/api/test-s3');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.sampleFiles) {
                        resultDiv.innerHTML += '<div class="test-result success">✅ S3 Bucket Contents: ' + data.objectCount + ' objects found</div>';
                        data.sampleFiles.forEach(file => {
                            if (file.key && !file.key.endsWith('/')) {
                                resultDiv.innerHTML += '<div class="test-result info">📁 ' + file.key + ' (' + Math.round(file.size/1024) + ' KB)</div>';
                            }
                        });
                    } else {
                        resultDiv.innerHTML += '<div class="test-result error">❌ S3 Bucket: No contents found</div>';
                    }
                } else {
                    resultDiv.innerHTML += '<div class="test-result error">❌ S3 Bucket Check: ERROR - Status ' + response.status + '</div>';
                }
            } catch (error) {
                resultDiv.innerHTML += '<div class="test-result error">❌ S3 Bucket Check: FAILED - ' + error.message + '</div>';
            }
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            setTimeout(() => {
                testReactServer();
                setTimeout(() => testBackendServer(), 1000);
                setTimeout(() => testS3Connectivity(), 2000);
            }, 500);
        };
    </script>
</body>
</html>
