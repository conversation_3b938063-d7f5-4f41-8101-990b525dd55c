<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="400" height="300" fill="#f5f5f5"/>
  
  <!-- Person silhouette -->
  <g fill="#000000">
    <!-- Head -->
    <ellipse cx="80" cy="80" rx="35" ry="45"/>
    <!-- Neck -->
    <rect x="70" y="120" width="20" height="25"/>
    <!-- Torso -->
    <rect x="50" y="145" width="60" height="120"/>
    <!-- Arms -->
    <rect x="20" y="160" width="25" height="80"/>
    <rect x="115" y="160" width="25" height="80"/>
  </g>
  
  <!-- Laptop/Screen -->
  <g fill="#000000">
    <!-- Screen -->
    <rect x="280" y="180" width="80" height="60" rx="5"/>
    <!-- Base -->
    <rect x="270" y="240" width="100" height="15" rx="7"/>
    <!-- Keyboard area -->
    <rect x="275" y="245" width="90" height="8" rx="2"/>
  </g>
  
  <!-- Distance arrow and text -->
  <g>
    <!-- Arrow line -->
    <line x1="120" y1="150" x2="270" y2="150" stroke="#000000" stroke-width="3"/>
    <!-- Left arrow head -->
    <polygon points="120,150 135,140 135,160" fill="#000000"/>
    <!-- Right arrow head -->
    <polygon points="270,150 255,140 255,160" fill="#000000"/>
    
    <!-- 30 cm text -->
    <text x="195" y="135" font-family="Arial, sans-serif" font-size="32" font-weight="bold" text-anchor="middle" fill="#000000">30 cm</text>
  </g>
</svg>
