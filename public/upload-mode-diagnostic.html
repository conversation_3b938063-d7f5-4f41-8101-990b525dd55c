<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Mode Diagnostic - ICU Dataset Application</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .diagnostic-section {
            background: white;
            margin: 15px 0;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { color: #4caf50; font-weight: bold; }
        .error { color: #f44336; font-weight: bold; }
        .warning { color: #ff9800; font-weight: bold; }
        .info { color: #2196f3; }
        button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #1976d2; }
        pre {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .status-box {
            border: 2px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status-success { border-color: #4caf50; background-color: #f1f8e9; }
        .status-error { border-color: #f44336; background-color: #ffebee; }
        .status-warning { border-color: #ff9800; background-color: #fff3e0; }
    </style>
</head>
<body>
    <h1>🔍 Upload Mode Diagnostic Tool</h1>
    <p>This tool diagnoses the exact upload mode and identifies why uploads are failing.</p>

    <div class="diagnostic-section">
        <h2>📋 Environment Detection</h2>
        <button onclick="runEnvironmentCheck()">Run Environment Check</button>
        <div id="env-status"></div>
    </div>

    <div class="diagnostic-section">
        <h2>🔧 Upload Mode Analysis</h2>
        <button onclick="analyzeUploadMode()">Analyze Upload Mode</button>
        <div id="upload-mode-status"></div>
    </div>

    <div class="diagnostic-section">
        <h2>🌐 Network Connectivity Test</h2>
        <button onclick="testNetworkConnectivity()">Test Network</button>
        <div id="network-status"></div>
    </div>

    <div class="diagnostic-section">
        <h2>🎥 Simulated Upload Test</h2>
        <button onclick="simulateUpload()">Simulate Upload</button>
        <div id="upload-test-status"></div>
    </div>

    <div class="diagnostic-section">
        <h2>📊 Console Logs</h2>
        <button onclick="clearLogs()">Clear Logs</button>
        <pre id="console-logs"></pre>
    </div>

    <script>
        let logs = [];
        
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;

        function captureLog(type, args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            logs.push(`[${type.toUpperCase()}] ${new Date().toLocaleTimeString()}: ${message}`);
            updateLogDisplay();
        }

        console.log = (...args) => {
            originalLog.apply(console, args);
            captureLog('log', args);
        };

        console.error = (...args) => {
            originalError.apply(console, args);
            captureLog('error', args);
        };

        console.warn = (...args) => {
            originalWarn.apply(console, args);
            captureLog('warn', args);
        };

        function updateLogDisplay() {
            document.getElementById('console-logs').textContent = logs.slice(-30).join('\n');
        }

        function clearLogs() {
            logs = [];
            updateLogDisplay();
        }

        function addStatus(elementId, html, statusClass = '') {
            const element = document.getElementById(elementId);
            const statusDiv = document.createElement('div');
            statusDiv.className = `status-box ${statusClass}`;
            statusDiv.innerHTML = html;
            element.appendChild(statusDiv);
        }

        function clearStatus(elementId) {
            document.getElementById(elementId).innerHTML = '';
        }

        async function runEnvironmentCheck() {
            clearStatus('env-status');
            
            // Check if we're in the React app
            const isReactApp = window.location.hostname === 'localhost' && window.location.port === '3003';
            
            if (isReactApp) {
                addStatus('env-status', '✅ Running in React development environment (localhost:3003)', 'status-success');
            } else {
                addStatus('env-status', '⚠️ Not running in React app - some tests may not work', 'status-warning');
            }

            // Check for React app globals
            const reactGlobals = {
                'process': typeof process !== 'undefined',
                'window.React': typeof window.React !== 'undefined',
                'window.awsStorage': typeof window.awsStorage !== 'undefined'
            };

            let globalsHtml = '<strong>React Environment Globals:</strong><br>';
            Object.entries(reactGlobals).forEach(([key, value]) => {
                globalsHtml += `${value ? '✅' : '❌'} ${key}: ${value}<br>`;
            });
            
            addStatus('env-status', globalsHtml, 'status-info');
        }

        async function analyzeUploadMode() {
            clearStatus('upload-mode-status');
            
            addStatus('upload-mode-status', '🔍 Analyzing upload mode configuration...', 'status-info');

            // Simulate the upload mode detection logic from awsStorage.js
            const envVars = {
                identityPool: 'ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd', // From .env
                region: 'ap-southeast-2',
                bucket: 'icudatasetphrasesfortesting'
            };

            const hasIdentityPool = !!envVars.identityPool;
            const hasRegion = !!envVars.region;
            const hasBucket = !!envVars.bucket;
            const forceBackendMode = false; // From awsStorage.js

            const shouldUseDirect = hasIdentityPool && hasRegion && hasBucket && !forceBackendMode;

            let modeHtml = '<strong>Upload Mode Analysis:</strong><br>';
            modeHtml += `✅ Identity Pool: ${hasIdentityPool ? 'SET' : 'MISSING'}<br>`;
            modeHtml += `✅ Region: ${hasRegion ? 'SET' : 'MISSING'}<br>`;
            modeHtml += `✅ Bucket: ${hasBucket ? 'SET' : 'MISSING'}<br>`;
            modeHtml += `✅ Force Backend Mode: ${forceBackendMode ? 'TRUE' : 'FALSE'}<br>`;
            modeHtml += `<br><strong>Expected Mode: ${shouldUseDirect ? 'DIRECT S3' : 'BACKEND'}</strong>`;

            addStatus('upload-mode-status', modeHtml, shouldUseDirect ? 'status-success' : 'status-warning');

            // Test S3 client initialization simulation
            try {
                addStatus('upload-mode-status', '🔧 Testing S3 client initialization...', 'status-info');
                
                // This will fail in browser, but we can catch the specific error
                const { S3Client } = await import('https://cdn.skypack.dev/@aws-sdk/client-s3');
                addStatus('upload-mode-status', '✅ AWS SDK loaded successfully', 'status-success');
            } catch (error) {
                addStatus('upload-mode-status', `❌ AWS SDK load failed: ${error.message}`, 'status-error');
                addStatus('upload-mode-status', '💡 This explains why S3 client is not initializing', 'status-warning');
            }
        }

        async function testNetworkConnectivity() {
            clearStatus('network-status');
            
            // Test backend connectivity
            addStatus('network-status', '🔍 Testing backend connectivity...', 'status-info');
            
            try {
                const healthResponse = await fetch('http://localhost:5000/health');
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    addStatus('network-status', `✅ Backend health check passed - Status: ${healthData.status}`, 'status-success');
                } else {
                    addStatus('network-status', `❌ Backend health check failed: HTTP ${healthResponse.status}`, 'status-error');
                }
            } catch (error) {
                addStatus('network-status', `❌ Backend connectivity failed: ${error.message}`, 'status-error');
            }

            // Test upload endpoint
            try {
                const uploadResponse = await fetch('http://localhost:5000/upload', {
                    method: 'POST',
                    body: new FormData() // Empty form data
                });
                
                if (uploadResponse.status === 400) {
                    addStatus('network-status', '✅ Upload endpoint responding (expected 400 for empty request)', 'status-success');
                } else {
                    addStatus('network-status', `⚠️ Upload endpoint returned: HTTP ${uploadResponse.status}`, 'status-warning');
                }
            } catch (error) {
                addStatus('network-status', `❌ Upload endpoint test failed: ${error.message}`, 'status-error');
            }

            // Test CORS
            addStatus('network-status', '🔍 Testing CORS configuration...', 'status-info');
            try {
                const corsResponse = await fetch('http://localhost:5000/health', {
                    method: 'OPTIONS'
                });
                addStatus('network-status', `✅ CORS preflight successful: HTTP ${corsResponse.status}`, 'status-success');
            } catch (error) {
                addStatus('network-status', `❌ CORS test failed: ${error.message}`, 'status-error');
            }
        }

        async function simulateUpload() {
            clearStatus('upload-test-status');
            
            addStatus('upload-test-status', '🎥 Creating mock video blob...', 'status-info');
            
            try {
                // Create a small mock video blob
                const mockData = new Uint8Array(1024); // 1KB of data
                for (let i = 0; i < mockData.length; i++) {
                    mockData[i] = i % 256;
                }
                const mockBlob = new Blob([mockData], { type: 'video/webm' });
                
                addStatus('upload-test-status', `✅ Mock video created: ${mockBlob.size} bytes`, 'status-success');

                // Test direct backend upload
                addStatus('upload-test-status', '🚀 Testing direct backend upload...', 'status-info');
                
                const formData = new FormData();
                formData.append('video', mockBlob, 'diagnostic-test.webm');
                formData.append('phrase', 'diagnostic-test');
                formData.append('demographics', JSON.stringify({
                    userId: 'diagnostic-user',
                    ageGroup: '40to64',
                    gender: 'female',
                    ethnicity: 'caucasian'
                }));
                formData.append('recordingNumber', '1');

                const uploadResponse = await fetch('http://localhost:5000/upload', {
                    method: 'POST',
                    body: formData
                });

                if (uploadResponse.ok) {
                    const result = await uploadResponse.json();
                    addStatus('upload-test-status', '✅ Backend upload successful!', 'status-success');
                    addStatus('upload-test-status', `📊 Result: ${JSON.stringify(result, null, 2)}`, 'status-info');
                } else {
                    const errorText = await uploadResponse.text();
                    addStatus('upload-test-status', `❌ Backend upload failed: HTTP ${uploadResponse.status}`, 'status-error');
                    addStatus('upload-test-status', `Error: ${errorText}`, 'status-error');
                }

            } catch (error) {
                addStatus('upload-test-status', `❌ Upload simulation failed: ${error.message}`, 'status-error');
                addStatus('upload-test-status', `Stack: ${error.stack}`, 'status-error');
            }
        }

        // Auto-run environment check on load
        window.addEventListener('load', () => {
            runEnvironmentCheck();
        });
    </script>
</body>
</html>
