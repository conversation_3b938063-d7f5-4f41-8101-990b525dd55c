[build]
  command = "npm run build"
  publish = "build"

[build.environment]
  # Production Environment Variables for ICU Dataset Application
  REACT_APP_AWS_IDENTITY_POOL_ID = "ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd"
  REACT_APP_AWS_REGION = "ap-southeast-2"
  REACT_APP_S3_BUCKET = "icudatasetphrasesfortesting"
  REACT_APP_BACKEND_URL = "http://3.106.117.128:5000"
  REACT_APP_NAME = "ICU Dataset Application"
  REACT_APP_VERSION = "1.0.0"
  NODE_ENV = "production"
  REACT_APP_DEBUG = "false"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[context.production]
  environment = { REACT_APP_BACKEND_URL = "http://3.106.117.128:5000" }

[context.deploy-preview]
  environment = { REACT_APP_BACKEND_URL = "http://3.106.117.128:5000" }

[context.branch-deploy]
  environment = { REACT_APP_BACKEND_URL = "http://3.106.117.128:5000" }
