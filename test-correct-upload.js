#!/usr/bin/env node

/**
 * Test Correct Upload - Verify upload with proper metadata format
 */

require('dotenv').config();
const FormData = require('form-data');
const fetch = require('node-fetch');
const fs = require('fs');

console.log('🎯 TESTING CORRECT UPLOAD FORMAT');
console.log('================================');

async function testCorrectUpload() {
  try {
    console.log('\n1. 📋 Creating test video data...');
    
    // Create a realistic test video blob
    const testVideoData = Buffer.from('WEBM_VIDEO_DATA_SIMULATION_' + Date.now());
    console.log('   - Video size:', testVideoData.length, 'bytes');
    
    console.log('\n2. 📤 Preparing FormData with correct metadata...');
    
    const formData = new FormData();
    formData.append('video', testVideoData, 'test_upload_correct.webm');
    formData.append('phrase', 'I need water');
    formData.append('category', 'Basic Needs');
    formData.append('recordingNumber', '1');
    
    // Create demographics object matching the expected format
    const demographics = {
      userId: 'test-user-' + Date.now(),
      ageGroup: '40to64',
      gender: 'female',
      ethnicity: 'caucasian',
      category: 'Basic Needs'  // Include category in demographics too
    };
    
    formData.append('demographics', JSON.stringify(demographics));
    
    console.log('   - FormData fields prepared:');
    console.log('     * video: test_upload_correct.webm (' + testVideoData.length + ' bytes)');
    console.log('     * phrase: I need water');
    console.log('     * category: Basic Needs');
    console.log('     * recordingNumber: 1');
    console.log('     * demographics:', JSON.stringify(demographics, null, 2));
    
    console.log('\n3. 🌐 Sending upload request to backend...');
    
    const response = await fetch('http://localhost:5000/upload', {
      method: 'POST',
      body: formData
    });
    
    console.log('   - Response status:', response.status, response.statusText);
    
    if (response.ok) {
      const result = await response.json();
      console.log('\n✅ UPLOAD SUCCESSFUL!');
      console.log('📊 Upload result:', JSON.stringify(result, null, 2));
      
      if (result.success) {
        console.log('\n🎉 Video successfully uploaded to S3!');
        console.log('📁 File path:', result.filePath);
        console.log('🔗 URL:', result.url);
        console.log('📏 Size:', result.size, 'bytes');
      } else {
        console.log('\n⚠️ Upload completed but with issues:', result.message);
      }
    } else {
      const errorText = await response.text();
      console.log('\n❌ UPLOAD FAILED');
      console.log('📄 Error response:', errorText);
    }
    
    console.log('\n4. 🔍 Verifying S3 bucket contents...');
    
    const s3TestResponse = await fetch('http://localhost:5000/api/test-s3');
    if (s3TestResponse.ok) {
      const s3Data = await s3TestResponse.json();
      console.log('   - Bucket object count:', s3Data.objectCount);
      console.log('   - Recent files:', s3Data.sampleFiles?.slice(0, 3).map(f => f.key) || []);
    }
    
  } catch (error) {
    console.error('\n❌ TEST FAILED:', error.message);
    console.error('🔧 Error details:', error.stack);
  }
}

// Run the test
testCorrectUpload();
