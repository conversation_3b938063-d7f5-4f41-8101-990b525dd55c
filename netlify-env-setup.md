# Netlify Environment Variables Setup for ICU Dataset Application

## 🚨 CRITICAL: Configure These Environment Variables in Netlify

### **Step 1: Access Netlify Environment Variables**
1. Go to [Netlify Dashboard](https://app.netlify.com)
2. Select your site: **icuphrasecollection.com**
3. Go to **Site settings** → **Environment variables**

### **Step 2: Add Production Environment Variables**

Add these **EXACT** environment variables in Netlify:

```
REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd
REACT_APP_AWS_REGION=ap-southeast-2
REACT_APP_S3_BUCKET=icudatasetphrasesfortesting
REACT_APP_BACKEND_URL=http://*************:5000
REACT_APP_NAME=ICU Dataset Application
REACT_APP_VERSION=1.0.0
NODE_ENV=production
REACT_APP_DEBUG=false
```

### **Step 3: Verify Configuration**

**CRITICAL VARIABLES FOR VIDEO UPLOAD:**
- ✅ `REACT_APP_BACKEND_URL=http://*************:5000` (Your EC2 server)
- ✅ `REACT_APP_AWS_IDENTITY_POOL_ID=ap-southeast-2:59baeaf9-a80c-4afc-8f27-f4d6191242dd`
- ✅ `REACT_APP_AWS_REGION=ap-southeast-2`
- ✅ `REACT_APP_S3_BUCKET=icudatasetphrasesfortesting`

### **Step 4: Redeploy After Adding Variables**
After adding environment variables, trigger a new deployment:
1. Go to **Deploys** tab
2. Click **Trigger deploy** → **Deploy site**
3. Wait for build to complete

## 🔧 Backend Server Requirements

### **Your EC2 Server Must Be:**
- ✅ **Running on**: `*************:5000`
- ✅ **CORS configured** for: `https://icuphrasecollection.com`
- ✅ **Security group** allows port 5000 from 0.0.0.0/0
- ✅ **Endpoints working**: `/health`, `/upload`

### **Test Backend Connectivity:**
```bash
curl -X GET http://*************:5000/health
```

Should return:
```json
{
  "status": "healthy",
  "uptime": 123.45,
  "services": {
    "aws": "connected"
  }
}
```

## 🎯 Expected Result After Fix

### **Before Fix (BROKEN):**
- ❌ Frontend tries to connect to `localhost:5000`
- ❌ Users get "network error during upload"
- ❌ Videos cannot be uploaded from https://icuphrasecollection.com

### **After Fix (WORKING):**
- ✅ Frontend connects to `http://*************:5000`
- ✅ Users can upload videos successfully
- ✅ Enhanced 1.5x video capture works in production
- ✅ Medical dataset collection fully functional

## 🚨 URGENT: Complete This Setup IMMEDIATELY

The ICU Dataset Application is currently **NON-FUNCTIONAL** for users accessing https://icuphrasecollection.com because the frontend cannot connect to the backend server.

**This is blocking medical data collection for ICU patient communication research.**
